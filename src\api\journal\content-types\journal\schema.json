{"kind": "collectionType", "collectionName": "journals", "info": {"singularName": "journal", "pluralName": "journals", "displayName": "多业务库/OA期刊"}, "options": {"comment": ""}, "extUniqueField": ["identifier"], "extMd5Field": ["<PERSON><PERSON><PERSON>", "doi", "title", "author", "page_num"], "attributes": {"abstract": {"extExcelTitle": "article_abstract", "type": "text"}, "article_trans_abstract": {"extExcelTitle": "article_trans_abstract", "type": "text"}, "article_trans_subtitle": {"extExcelTitle": "article_trans_subtitle", "type": "text"}, "article_trans_title": {"extExcelTitle": "article_trans_title", "type": "text"}, "article_type": {"extExcelTitle": "article_type", "type": "string"}, "auth_email": {"extExcelTitle": "auth_email", "type": "text"}, "author": {"extExcelTitle": "author", "type": "text"}, "author_org": {"extExcelTitle": "auth_institution", "type": "text"}, "city": {"extExcelTitle": "city", "type": "text"}, "classification": {"extExcelTitle": "classification", "type": "string"}, "country": {"extExcelTitle": "country", "type": "text"}, "database_id": {"extExcelTitle": "database_id", "type": "string"}, "detailurl": {"extExcelTitle": "file_path", "type": "text"}, "doi": {"extExcelTitle": "doi", "type": "string"}, "end_page": {"extExcelTitle": "article_lpage", "type": "string"}, "files": {"type": "string", "extExcelTitle": "file_name", "extRelatedTable": {"associatedFieldName": "files_normal", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:"}}, "files_normal": {"type": "text", "extIsFieldExported": false, "extIsValidationRequired": false}, "file_path_url": {"extExcelTitle": "download_path", "type": "text"}, "full_name": {"extExcelTitle": "full_name", "type": "string"}, "identifier": {"extExcelTitle": "article_caid", "type": "string"}, "unique_md5": {"type": "string", "extIsFieldExported": false}, "institution": {"extExcelTitle": "institution", "type": "string"}, "institution_id": {"extExcelTitle": "institution_id", "type": "string"}, "issue": {"extExcelTitle": "source_issue", "type": "string"}, "issue_part": {"extExcelTitle": "issue_part", "type": "string"}, "issue_total": {"extExcelTitle": "issue_total", "type": "string"}, "keyword": {"extExcelTitle": "keyword", "type": "text"}, "keyword_language": {"extExcelTitle": "keyword_language", "type": "string"}, "language": {"extExcelTitle": "article_language", "type": "string"}, "oa_id": {"extExcelTitle": "id", "type": "string"}, "page_info": {"extExcelTitle": "article_page_range", "type": "string"}, "page_num": {"extExcelTitle": "article_page_count", "type": "string"}, "postal_code": {"extExcelTitle": "postal_code", "type": "text"}, "process_date": {"extExcelTitle": "process_date", "type": "string"}, "process_level": {"extExcelTitle": "process_level", "type": "string"}, "process_mode": {"extExcelTitle": "process_mode", "type": "string"}, "pub_year": {"extExcelTitle": "source_year", "type": "string"}, "source_id": {"extExcelTitle": "source_id", "type": "string"}, "source_issn": {"extExcelTitle": "source_issn", "type": "text"}, "source_title": {"extExcelTitle": "source_title", "type": "text"}, "start_page": {"extExcelTitle": "article_fpage", "type": "string"}, "state": {"extExcelTitle": "state", "type": "text"}, "subtitle": {"extExcelTitle": "article_subtitle", "type": "text"}, "supplement": {"extExcelTitle": "supplement", "type": "string"}, "title": {"extExcelTitle": "article_title", "type": "text"}, "volume": {"extExcelTitle": "source_volume", "type": "string"}, "volume_id": {"extExcelTitle": "volume_id", "type": "string"}}}