'use strict'
module.exports = {
  async beforeCreate(event) {
    await strapi.plugin('extend-admin-api').service('common').beforeCreate(event)
  },
  async beforeUpdate(event) {
    await strapi.plugin('extend-admin-api').service('common').beforeUpdate(event)
  },
  async afterUpdate(event) {
    await strapi.plugin('extend-admin-api').service('common').afterUpdate(event)
  },
  async afterDelete(event) {
    await strapi.plugin('extend-admin-api').service('common').afterDelete(event)
  },
}
