'use strict'

/**
 * paper controller
 */

const { createCoreController } = require('@strapi/strapi').factories
const { errors } = require('@strapi/utils')

const { ApplicationError } = errors
module.exports = createCoreController('api::paper.paper', ({ strapi }) => ({
  /**
   * 论文主页类别统计
   * @param ctx
   * @returns {Promise<void>}
   */
  async categoryStatistics(ctx) {
    ctx.body = await strapi.service(`api::paper.paper`).categoryStatistics()
  },

  /**
   * 修改文件
   * @param ctx
   * @returns {Promise<void>}
   */
  async updateFile(ctx) {
    ctx.body = await strapi.service(`api::paper.paper`).updateFile()
  },

  /**
   * 获取pdf
   * @param ctx
   * @returns {Promise<void>}
   */
  async validatePermissions(ctx) {
    ctx.body = await strapi.service(`api::paper.paper`).validatePermissions(ctx)
  },

  /**
   * 访问统计
   * @param ctx
   * @returns {Promise<void>}
   */
  async accessStatistics(ctx) {
    const { status, documentId } = ctx.request.body
    if (!status) {
      throw new ApplicationError('缺少统计数据')
    }
    if (!documentId) {
      throw new ApplicationError('缺少documentId')
    }
    ctx.body = await strapi.service(`api::paper.paper`).accessStatistics(ctx)
  },
}))
