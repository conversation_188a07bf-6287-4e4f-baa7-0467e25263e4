'use strict'

/**
 * common controller
 */

const { errors } = require('@strapi/utils')

const uuid = require('uuid')

const { ApplicationError } = errors

module.exports = {
  async handleFormat(userID, documentIds, type) {
    // 查询
    const filters = {
      normed: false,
    }
    if (documentIds && documentIds.length > 0) {
      filters.documentId = {
        $in: documentIds,
      }
    }
    const list = await strapi.documents(`api::${type}.${type}`).findMany({
      filters,
    })
    if (!list.length) {
      return { result: null, message: '数据已全部标准化' }
    }
    const formatResult = await strapi.plugin('extend-admin-api').service('common').formatData({ data: list, type, userID })

    const excelData = []
    if (formatResult && formatResult.errorMessage && formatResult.errorMessage.length > 0) {
      const mergeData = {}
      formatResult.errorMessage.forEach((v) => {
        delete v.index
        delete v.id
        Object.keys(v).forEach((key) => {
          if (!mergeData[key]) {
            mergeData[key] = []
          }
          mergeData[key] = Array.from(new Set(mergeData[key].concat(v[key].value)))
        })
      })

      Object.keys(mergeData).forEach((key) => {
        const obj = {}
        obj['字段名称'] = strapi.plugin('extend-admin-api').service('common').getContentTypes(type).attributes[key].extExcelTitle
        obj['字段编码'] = key
        obj['字段值'] = mergeData[key].toString()
        excelData.push(obj)
      })
      if (excelData && excelData.length > 0) {
        const filePath = `/validation_errors/${uuid.v7()}.xlsx`
        await strapi.plugin('extend-admin-api').service('excel').writeData(excelData, `${process.cwd()}/public${filePath}`)
        throw new ApplicationError('excel存在错误数据', { filePath })
      }
    }
    return { result: null, message: '数据已完成标准化' }
  },
  /**
   * 格式化normal数据
   * @param ctx
   * @returns {Promise<void>}
   */
  async formatData(ctx) {
    if (!ctx.request.body || !ctx.request.body.type) {
      return ctx.badRequest('缺少文件字段')
    }
    const userID = ctx.state.user.id
    const type = ctx.request.body.type
    const documentIds = ctx.request.body.documentIds

    try {
      ctx.body = await this.handleFormat(userID, documentIds, type)
    }
    catch (e) {
      return ctx.throw(400, e)
    }
  },
}
