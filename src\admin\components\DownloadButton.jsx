import { download } from '../common/fetch'
import { DownloadOutlined } from '@ant-design/icons'
import { Button, message } from 'antd'

import * as React from 'react'
import { useLocation, useParams } from 'react-router-dom'

function DownloadButton() {
      const params = useParams()

  const { search } = useLocation()
  const [isLoading, setIsLoading] = React.useState(false)

  const singularName = params.slug.split('.')[1]

  const handleClick = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    try {
      await download(`/extend-admin-api/download${search}&type=${singularName}`, {
        filename: `${singularName}-export`,
      })
    }
    catch (err) {
            message.error(err?.response?.data?.error?.message || err.message|| '下载失败' )

    }
    finally {
      setIsLoading(false)
    }
  }

  return (
      <Button loading={isLoading} icon={<DownloadOutlined />} onClick={handleClick}> 导出</Button>

  )
}

export { DownloadButton }
