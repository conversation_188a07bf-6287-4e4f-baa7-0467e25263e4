{"kind": "collectionType", "collectionName": "enum_country", "info": {"singularName": "enum-country", "pluralName": "enum-countries", "displayName": "海外文献库/规范-国家"}, "options": {"draftAndPublish": false}, "extUniqueField": ["name", "code"], "pluginOptions": {}, "attributes": {"name": {"extExcelTitle": "中文名称", "type": "string", "required": true, "unique": true, "regex": "^(?!\\s*$).+"}, "name_en": {"extExcelTitle": "英文名称", "type": "string"}, "code": {"type": "string", "extExcelTitle": "标准代码", "unique": true}, "name1": {"extExcelTitle": "名称1", "type": "string"}, "name2": {"extExcelTitle": "名称2", "type": "string"}, "name3": {"extExcelTitle": "名称3", "type": "string"}, "name4": {"extExcelTitle": "名称4", "type": "string"}, "name5": {"extExcelTitle": "名称5", "type": "string"}, "name6": {"extExcelTitle": "名称6", "type": "string"}, "name7": {"extExcelTitle": "名称7", "type": "string"}, "name8": {"extExcelTitle": "名称8", "type": "string"}, "name9": {"extExcelTitle": "名称9", "type": "string"}, "parent": {"extExcelTitle": "父节点", "type": "relation", "relation": "oneToOne", "target": "api::enum-country.enum-country"}}}