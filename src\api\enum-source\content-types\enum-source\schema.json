{"kind": "collectionType", "collectionName": "enum_sources", "info": {"singularName": "enum-source", "pluralName": "enum-sources", "displayName": "海外文献库/规范-来源网站"}, "options": {"draftAndPublish": false}, "extUniqueField": ["name", "code"], "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true, "extExcelTitle": "中文名称", "regex": "^(?!\\s*$).+"}, "code": {"type": "string", "extExcelTitle": "标准代码", "unique": true}, "name_en": {"type": "string", "extExcelTitle": "英文名称"}, "name_short": {"type": "string", "extExcelTitle": "缩略名称"}, "host": {"type": "string", "extExcelTitle": "网站地址"}}}