{"kind": "collectionType", "collectionName": "conferences", "info": {"singularName": "conference", "pluralName": "conferences", "displayName": "多业务库/OA会议"}, "options": {"comment": ""}, "extUniqueField": ["identifier"], "extMd5Field": ["<PERSON><PERSON><PERSON>", "doi", "title", "author", "page_num"], "attributes": {"abstract": {"extExcelTitle": "article_abstract", "type": "text"}, "article_trans_abstract": {"extExcelTitle": "article_trans_abstract", "type": "text"}, "article_trans_subtitle": {"extExcelTitle": "article_trans_subtitle", "type": "text"}, "article_trans_title": {"extExcelTitle": "article_trans_title", "type": "text"}, "article_type": {"extExcelTitle": "article_type", "type": "string"}, "auth_email": {"extExcelTitle": "auth_email", "type": "text"}, "author": {"extExcelTitle": "contrib_full_name", "type": "text"}, "author_org": {"extExcelTitle": "auth_institution", "type": "text"}, "database_id": {"extExcelTitle": "database_id", "type": "string"}, "detailurl": {"extExcelTitle": "file_path", "type": "text"}, "doi": {"extExcelTitle": "doi", "type": "string"}, "end_page": {"extExcelTitle": "article_lpage", "type": "string"}, "files": {"type": "string", "extExcelTitle": "file_name", "extRelatedTable": {"associatedFieldName": "files_normal", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:"}}, "files_normal": {"type": "text", "extIsFieldExported": false, "extIsValidationRequired": false}, "file_path_url": {"extExcelTitle": "download_path", "type": "text"}, "full_name": {"extExcelTitle": "full_name", "type": "string"}, "identifier": {"extExcelTitle": "article_caid", "type": "string"}, "unique_md5": {"type": "string", "extIsFieldExported": false}, "institution": {"extExcelTitle": "institution", "type": "string"}, "institution_id": {"extExcelTitle": "institution_id", "type": "string"}, "keyword": {"extExcelTitle": "keyword", "type": "text"}, "language": {"extExcelTitle": "article_language", "type": "string"}, "oa_id": {"extExcelTitle": "id", "type": "string"}, "page_info": {"extExcelTitle": "article_page_range", "type": "string"}, "page_num": {"extExcelTitle": "article_page_count", "type": "string"}, "process_date": {"extExcelTitle": "process_date", "type": "string"}, "process_level": {"extExcelTitle": "process_level", "type": "string"}, "process_mode": {"extExcelTitle": "process_mode", "type": "string"}, "source_id": {"extExcelTitle": "source_id", "type": "string"}, "source_issn": {"extExcelTitle": "source_issn", "type": "text"}, "source_title": {"extExcelTitle": "source_title", "type": "text"}, "start_page": {"extExcelTitle": "article_fpage", "type": "string"}, "subtitle": {"extExcelTitle": "article_subtitle", "type": "text"}, "t_num": {"extExcelTitle": "T号", "type": "string"}, "title": {"extExcelTitle": "article_title", "type": "text"}, "trans_keyword": {"extExcelTitle": "trans_keyword", "type": "string"}, "trans_language": {"extExcelTitle": "trans_language", "type": "string"}}}