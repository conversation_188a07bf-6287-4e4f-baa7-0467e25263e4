const fs = require('node:fs')
const path = require('node:path')

module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    if (ctx.path.includes('validation_errors')) {
      const publicDir = path.join(strapi.dirs.static.public, 'validation_errors')
      const fileName = ctx.path && ctx.path.split('/')[2] ? ctx.path.split('/')[2] : ''
      const requestedFile = path.join(publicDir, fileName)
      // 检查请求的文件是否存在于 uploads 目录中
      if (fs.existsSync(requestedFile)) {
        await next() // 继续执行其他中间件

        // 确保响应结束后删除文件
        ctx.res.on('finish', () => {
          fs.unlink(requestedFile, (err) => {
            if (err) {
              strapi.log.error(`Failed to delete file: ${requestedFile}`, err)
            }
            else {
              strapi.log.info(`File deleted: ${requestedFile}`)
            }
          })
        })
      }
      else {
        await next() // 如果文件不存在，继续执行其他中间件
      }
    }
    else {
      await next()
    }
  }
}
