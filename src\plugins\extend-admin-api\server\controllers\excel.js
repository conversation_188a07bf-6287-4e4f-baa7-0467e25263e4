'use strict'
const fs = require('node:fs')
/**
 * excel controller
 */

module.exports = {
  /**
   * 上传excel
   * @param ctx
   * @returns {Promise<{data: *}|Koa.Context>}
   */
  async upload(ctx) {
    const files = ctx.request.files
    const type = ctx.request.body.type
    const userID = ctx.state.user.id
    if (!type)
      return ctx.badRequest('缺少文件字段')

    // 读取表格文件
    if (!files || !files.file.filepath)
      return ctx.badRequest('缺少文件字段')
    let responseResult = null

    try {
      responseResult = await strapi.plugin('extend-admin-api').service('excel').upload(files.file.filepath, type, userID)

      if (responseResult && responseResult.insertResult !== undefined && responseResult.updateResult !== undefined) {
        // 记录上传记录
        await strapi.plugin('extend-admin-api').service('excel').createExcelLog(files.file, type, responseResult.insertResult.length, responseResult.updateResult.length, userID)
      }

      return { data: responseResult }
    }
    catch (e) {
      return ctx.throw(400, e)
    }
  },
  /**
   * 下载excel
   * @param ctx
   * @returns {Promise<Koa.Context>}
   */
  async download(ctx) {
    try {
      const type = ctx.query.type
      if (!type)
        return ctx.badRequest('缺少文件字段')
      delete ctx.query.type

      const contentTypes = strapi.plugin('extend-admin-api').service('common').getContentTypes(type)
      if (!contentTypes)
        return ctx.badRequest('无法获取文件规则')

      const uid = `api::${type}.${type}`
      const contentType = strapi.contentType(uid)
      const queryParams = {}
      if (ctx.query.populate === undefined)
        queryParams.populate = '*'

      const sanitizedQueryParams = await strapi.contentAPI.sanitize.query(
        { ...ctx.query, ...queryParams },
        contentType,
      )
      const filePath = await strapi.plugin('extend-admin-api').service('excel').download({
        uid,
        contentType,
        query: sanitizedQueryParams,
        rules: contentTypes,
      })

      ctx.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      ctx.set('Content-Disposition', `attachment;filename=${encodeURIComponent(`${contentTypes.name}`)}.xlsx`)
      ctx.body = fs.createReadStream(filePath)

      ctx.res.on('finish', () => {
        fs.unlink(filePath, () => {})
      })
    }
    catch (e) {
      ctx.throw(400, e)
    }
  },
}
