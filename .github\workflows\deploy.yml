name: Deploy to Server

on:
  push:
    branches:
      - master
jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      DEPLOY_PATH: /opt/project/facisp-document-api
      PROJECT_NAME: facisp-document-api

    steps:
      - uses: actions/checkout@master
      - uses: actions/setup-node@master
        with:
          node-version: 22
          cache: 'npm'
      - run: npm ci
      - run: npm run build

      - name: 清理目录
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            cd ${{ env.DEPLOY_PATH }}
            rm -rf build config src

      - name: 上传项目编译文件
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          source: "build,config,public,src,.env,docker-compose.yml,favicon.ico,package.json,package-lock.json"
          target: "${{ env.DEPLOY_PATH }}"
#          strip_components: 1

      - name: 部署应用
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            cd ${{ env.DEPLOY_PATH }}
            docker compose down
            docker compose up -d
      - name: Get last git commit info
        id: gitlog
        run: |
          echo "msg=$(git log -1 --pretty=%s)" >> $GITHUB_OUTPUT
          echo "author=$(git log -1 --pretty='%an')" >> $GITHUB_OUTPUT
          echo "date=$(git log -1 --date=iso-local --pretty='%cd')" >> $GITHUB_OUTPUT
      - name: Notify Feishu
        if: always()
        env:
          FEISHU_WEBHOOK: "https://open.feishu.cn/open-apis/bot/v2/hook/28750313-f770-4316-bb04-b25cf68a0294"
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            STATUS_TEXT="✅ CI 任务成功"
          else
            STATUS_TEXT="❌ CI 任务失败"
          fi

          MESSAGE_CONTENT='{
            "msg_type": "text",
            "content": {
              "text": "'"${STATUS_TEXT}"'\n⏱️ 提交说明: ${{ steps.gitlog.outputs.msg }}\n📦 项目: ${{ github.repository }}\n🌿 分支: ${{ github.ref_name }}\n👤 作者: ${{ github.actor }}\n🔗 链接: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }
          }'

          curl -X POST "$FEISHU_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "$MESSAGE_CONTENT"
