import { get } from './common/fetch'
import { DownloadButton } from './components/DownloadButton'
import { NormaliseButton } from './components/NormaliseButton'

import { UploadButton } from './components/UploadButton'
// import { Button } from '@strapi/design-system'
// @ts-ignore
import Logo from './extensions/favicon.png'

export default {
  config: {
    locales: ['zh-<PERSON>'],
    translations: {
      'zh-<PERSON>': {
        // 全局
        'global.password': '密码',
        'global.content-manager': '内容管理',
        'global.home': '首页',
        // 登陆相关
        'Auth.components.Oops.text': '您的账户已被暂停。',
        'Auth.components.Oops.text.admin': '如果这是误操作，请联系您的管理员。',
        'Auth.components.Oops.title': '哎呀...',
        'Auth.form.active.label': '活跃',
        'Auth.form.button.forgot-password': '发送邮件',
        'Auth.form.button.go-home': '返回首页',
        'Auth.form.button.login': '登录',
        'Auth.form.button.login.providers.error': '无法通过所选提供商连接您。',
        'Auth.form.button.login.strapi': '通过 Strapi 登录',
        'Auth.form.button.password-recovery': '密码恢复',
        'Auth.form.button.register': '让我们开始',
        'Auth.form.confirmPassword.label': '确认密码',
        'Auth.form.currentPassword.label': '当前密码',
        'Auth.form.email.label': '邮箱',
        'Auth.form.email.placeholder': '请输入邮箱',
        'Auth.form.error.blocked': '您的账户已被管理员封禁。',
        'Auth.form.error.code.provide': '提供的验证码不正确。',
        'Auth.form.error.confirmed': '您的账户邮箱尚未确认。',
        'Auth.form.error.email.invalid': '此邮箱无效。',
        'Auth.form.error.email.provide': '请输入您的用户名或邮箱。',
        'Auth.form.error.email.taken': '邮箱已被占用。',
        'Auth.form.error.invalid': '标识符或密码无效。',
        'Auth.form.error.params.provide': '提供的参数不正确。',
        'Auth.form.error.password.format': '您的密码不能包含超过三个 `$` 符号。',
        'Auth.form.error.password.local': '此用户从未设置本地密码，请通过账户创建时使用的提供商登录。',
        'Auth.form.error.password.matching': '两次输入的密码不一致。',
        'Auth.form.error.password.provide': '请输入您的密码。',
        'Auth.form.error.ratelimit': '尝试次数过多，请一分钟后再试。',
        'Auth.form.error.user.not-exist': '此邮箱不存在。',
        'Auth.form.error.username.taken': '用户名已被占用。',
        'Auth.form.firstname.label': '名',
        'Auth.form.firstname.placeholder': '请输入名',
        'Auth.form.forgot-password.email.label': '请输入您的邮箱',
        'Auth.form.forgot-password.email.label.success': '已成功发送邮件至',
        'Auth.form.lastname.label': '姓',
        'Auth.form.lastname.placeholder': '请输入姓',
        'Auth.form.password.hide-password': '隐藏密码',
        'Auth.form.password.hint': '必须至少包含8个字符，1个大写字母，1个小写字母和1个数字',
        'Auth.form.password.show-password': '显示密码',
        'Auth.form.register.news.label': '请告知我有关新功能和即将进行的改进（通过这样做，您接受 {terms} 和 {policy}）。',
        'Auth.form.register.subtitle': '凭据仅用于在 Strapi 中进行身份验证。所有保存的数据将存储在您的数据库中。',
        'Auth.form.rememberMe.label': '记住我',
        'Auth.form.username.label': '用户名',
        'Auth.form.username.placeholder': '请输入用户名',
        'Auth.form.welcome.subtitle': '登录您的账户',
        'Auth.form.welcome.title': '欢迎使用管理端',
        'Auth.link.forgot-password': '忘记密码？',
        'Auth.link.ready': '准备登录？',
        'Auth.link.signin': '登录',
        'Auth.link.signin.account': '已有账户？',
        'Auth.login.sso.divider': '或通过以下方式登录',
        'Auth.login.sso.loading': '正在加载提供商...',
        'Auth.login.sso.subtitle': '通过 SSO 登录到您的账户',
        'Auth.privacy-policy-agreement.policy': '隐私政策',
        'Auth.privacy-policy-agreement.terms': '服务条款',
        'Auth.reset-password.title': '重置密码',
        // 'app.components.HomePage.welcomeBlock.content': '通过左侧菜单内容管理查看和管理数据。',
        // 'app.components.HomePage.welcomeBlock.content.again': '通过左侧菜单内容管理查看和管理数据。',
        // 'app.components.LeftMenu.navbrand.title': '控制面板',
        // 内容管理

        'content-manager.plugin.name': '内容管理',
        'content-manager.App.schemas.data-loaded': '模式已成功加载',
        'content-manager.actions.clone.error': '克隆文档时发生错误。',
        'content-manager.actions.clone.label': '复制',
        'content-manager.actions.delete.dialog.body': '您确定要删除此文档吗？此操作不可逆。',
        'content-manager.actions.delete.error': '删除文档时发生错误。',
        'content-manager.actions.delete.label': '删除条目{isLocalized, select, true { (所有语言)} other {}}',
        'content-manager.actions.discard.label': '放弃更改',
        'content-manager.actions.discard.dialog.body': '您确定要放弃更改吗？此操作不可逆。',
        'content-manager.actions.edit.error': '编辑文档时发生错误。',
        'content-manager.actions.edit.label': '编辑',
        'content-manager.actions.unpublish.error': '取消发布文档时发生错误。',
        'content-manager.actions.unpublish.dialog.body': '您确定要取消发布此文档吗？',
        'content-manager.actions.unpublish.dialog.option.keep-draft': '取消发布并保留最新草稿',
        'content-manager.actions.unpublish.dialog.option.replace-draft': '取消发布并替换最新草稿',
        'content-manager.ListViewTable.relation-loaded': '关系已加载',
        'content-manager.ListViewTable.relation-loading': '关系正在加载',
        'content-manager.ListViewTable.relation-more': '此关系包含比显示的更多的实体',
        'content-manager.EditRelations.title': '关系数据',
        'content-manager.HeaderLayout.button.label-add-entry': '创建新条目',
        'content-manager.api.id': 'API ID',
        'content-manager.apiError.This attribute must be unique': '{field} 必须唯一',
        'content-manager.components.AddFilterCTA.add': '筛选器',
        'content-manager.components.AddFilterCTA.hide': '筛选器',
        'content-manager.components.DragHandle-label': '拖动',
        'content-manager.components.DraggableAttr.edit': '点击以编辑',
        'content-manager.components.DraggableCard.delete.field': '删除 {item}',
        'content-manager.components.DraggableCard.edit.field': '编辑 {item}',
        'content-manager.components.DraggableCard.move.field': '移动 {item}',
        'content-manager.components.ListViewTable.row-line': '项目行 {number}',
        'content-manager.components.DynamicZone.ComponentPicker-label': '选择一个组件',
        'content-manager.components.DynamicZone.add-component': '向 {componentName} 添加组件',
        'content-manager.components.DynamicZone.delete-label': '删除 {name}',
        'content-manager.components.DynamicZone.error-message': '组件包含错误',
        'content-manager.components.DynamicZone.missing-components': '有 {number, plural, =0 {# 个缺失的组件} one {# 个缺失的组件} other {# 个缺失的组件}}',
        'content-manager.components.DynamicZone.extra-components': '有 {number, plural, =0 {# 个额外的组件} one {# 个额外的组件} other {# 个额外的组件}}',
        'content-manager.components.DynamicZone.move-down-label': '向下移动组件',
        'content-manager.components.DynamicZone.move-up-label': '向上移动组件',
        'content-manager.components.DynamicZone.pick-compo': '选择一个组件',
        'content-manager.components.DynamicZone.required': '组件是必需的',
        'content-manager.components.EmptyAttributesBlock.button': '转到设置页面',
        'content-manager.components.EmptyAttributesBlock.description': '您可以更改设置',
        'content-manager.components.FieldItem.linkToComponentLayout': '设置组件布局',
        'content-manager.components.FieldSelect.label': '添加字段',
        'content-manager.components.FilterOptions.button.apply': '应用',
        'content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply': '应用',
        'content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll': '清除全部',
        'content-manager.components.FiltersPickWrapper.PluginHeader.description': '设置应用于过滤条目的条件',
        'content-manager.components.FiltersPickWrapper.PluginHeader.title.filter': '筛选器',
        'content-manager.components.FiltersPickWrapper.hide': '隐藏',
        'content-manager.components.Filters.usersSelect.label': '搜索并选择一个用户以进行过滤',
        'content-manager.components.LeftMenu.Search.label': '搜索内容类型',
        'content-manager.components.LeftMenu.collection-types': '集合类型',
        'content-manager.components.LeftMenu.single-types': '单个类型',
        'content-manager.components.LimitSelect.itemsPerPage': '每页项目数',
        'content-manager.components.NotAllowedInput.text': '没有权限查看此字段',
        'content-manager.components.RelationInput.icon-button-aria-label': '拖动',
        'content-manager.components.RepeatableComponent.error-message': '组件包含错误',
        'content-manager.components.Search.placeholder': '搜索条目...',
        'content-manager.components.Select.draft-info-title': '草稿',
        'content-manager.components.Select.publish-info-title': '已发布',
        'content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings': '自定义编辑视图的外观。',
        'content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings': '定义列表视图的设置。',
        'content-manager.components.SettingsViewWrapper.pluginHeader.title': '配置视图 — {name}',
        'content-manager.bulk-publish.already-published': '已发布',
        'content-manager.bulk-unpublish.already-unpublished': '已取消发布',
        'content-manager.bulk-publish.modified': '准备发布更改',
        'content-manager.bulk-publish.waiting-for-action': '等待操作',
        'content-manager.components.TableDelete.delete': '删除全部',
        'content-manager.components.TableDelete.deleteSelected': '删除选中项',
        'content-manager.components.TableDelete.label': '已选中 {number, plural, one {# 条目} other {# 条目}}',
        'content-manager.components.TableEmpty.withFilters': '没有应用筛选器的 {contentType}...',
        'content-manager.components.TableEmpty.withSearch': '没有与搜索 ({search}) 对应的 {contentType}...',
        'content-manager.components.TableEmpty.withoutFilter': '没有 {contentType}...',
        'content-manager.components.empty-repeatable': '尚未有条目。点击以添加一个。',
        'content-manager.components.notification.info.maximum-requirement': '您已达到字段的最大数量',
        'content-manager.components.notification.info.minimum-requirement': '已添加一个字段以满足最小要求',
        'content-manager.components.repeatable.reorder.error': '重新排序组件字段时发生错误，请重试',
        'content-manager.components.reset-entry': '重置条目',
        'content-manager.components.uid.apply': '应用',
        'content-manager.components.uid.available': '可用',
        'content-manager.components.uid.regenerate': '重新生成',
        'content-manager.components.uid.suggested': '建议',
        'content-manager.components.uid.unavailable': '不可用',
        'content-manager.containers.edit.tabs.label': '文档状态',
        'content-manager.containers.edit.tabs.draft': '草稿',
        'content-manager.containers.edit.tabs.published': '已发布',
        'content-manager.containers.edit.panels.default.title': '条目',
        'content-manager.containers.edit.panels.default.more-actions': '更多文档操作',
        'content-manager.containers.Edit.delete': '删除',
        'content-manager.containers.edit.title.new': '创建条目',
        'content-manager.containers.edit.header.more-actions': '更多操作',
        'content-manager.containers.edit.information.last-published.label': '已发布',
        'content-manager.containers.edit.information.last-published.value': '{time}{isAnonymous, select, true {} other { 由 {author}}}',
        'content-manager.containers.edit.information.last-draft.label': '上次更新',
        'content-manager.containers.edit.information.last-draft.value': '{time}{isAnonymous, select, true {} other { 由 {author}}}',
        'content-manager.containers.edit.information.document.label': '创建时间',
        'content-manager.containers.edit.information.document.value': '{time}{isAnonymous, select, true {} other { 由 {author}}}',
        'content-manager.containers.EditSettingsView.modal-form.edit-field': '编辑字段',
        'content-manager.containers.EditView.add.new-entry': '添加条目',
        'content-manager.containers.EditView.notification.errors': '表单包含一些错误',
        'content-manager.containers.Home.introduction': '要编辑条目，请转到左侧菜单中的特定链接。此插件没有正确的方法来编辑设置，仍在积极开发中。',
        'content-manager.containers.Home.pluginHeaderDescription': '通过强大且美观的界面管理您的条目。',
        'content-manager.containers.Home.pluginHeaderTitle': '内容管理器',
        'content-manager.containers.List.draft': '草稿',
        'content-manager.containers.List.published': '已发布',
        'content-manager.containers.List.modified': '已修改',
        'content-manager.containers.list.displayedFields': '显示的字段',
        'content-manager.containers.list.items': '{number} {number, plural, =0 {项} one {项} other {项}}',
        'content-manager.containers.list.table.row-actions': '行操作',
        'content-manager.containers.list.selectedEntriesModal.title': '发布条目',
        'content-manager.containers.list.selectedEntriesModal.selectedCount.publish': '<b>{publishedCount}</b> {publishedCount, plural, =0 {条目} one {条目} other {条目}} 已发布。 <b>{draftCount}</b> {draftCount, plural, =0 {条目} one {条目} other {条目}} 准备发布。 <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {条目} one {条目} other {条目}} 等待操作。',
        'content-manager.containers.list.selectedEntriesModal.selectedCount.unpublish': '<b>{draftCount}</b> {draftCount, plural, =0 {条目} one {条目} other {条目}} 已取消发布。 <b>{publishedCount}</b> {publishedCount, plural, =0 {条目} one {条目} other {条目}} 准备取消发布。',
        'content-manager.containers.list.autoCloneModal.header': '复制',
        'content-manager.containers.list.autoCloneModal.title': '此条目无法直接复制。',
        'content-manager.containers.list.autoCloneModal.description': '将创建一个具有相同内容的新条目，但您必须更改以下字段才能保存它。',
        'content-manager.containers.list.autoCloneModal.create': '创建',
        'content-manager.containers.list.autoCloneModal.error.unique': '唯一字段中不允许有相同的值。',
        'content-manager.containers.list.autoCloneModal.error.relation': '复制关系可能会从原始条目中移除它。',
        'content-manager.containers.list-settings.modal-form.label': '编辑 {fieldName}',
        'content-manager.containers.list-settings.modal-form.error': '尝试打开表单时发生错误。',
        'content-manager.containers.edit-settings.modal-form.error': '尝试打开表单时发生错误。',
        'content-manager.containers.edit-settings.modal-form.label': '标签',
        'content-manager.containers.edit-settings.modal-form.description': '描述',
        'content-manager.containers.edit-settings.modal-form.placeholder': '占位符',
        'content-manager.containers.edit-settings.modal-form.mainField': '条目标题',
        'content-manager.containers.edit-settings.modal-form.mainField.hint': '在编辑和列表视图中设置显示的字段',
        'content-manager.containers.edit-settings.modal-form.editable': '可编辑字段',
        'content-manager.containers.edit-settings.modal-form.size': '大小',
        'content-manager.containers.SettingPage.add.field': '插入另一个字段',
        'content-manager.containers.SettingPage.add.relational-field': '插入另一个相关字段',
        'content-manager.containers.SettingPage.attributes': '属性字段',
        'content-manager.containers.SettingPage.attributes.description': '定义属性的顺序',
        'content-manager.containers.SettingPage.editSettings.description': '通过拖放字段来构建布局',
        'content-manager.containers.SettingPage.editSettings.entry.title': '条目标题',
        'content-manager.containers.SettingPage.editSettings.entry.title.description': '设置条目的显示字段',
        'content-manager.containers.SettingPage.editSettings.relation-field.description': '在编辑和列表视图中设置显示的字段',
        'content-manager.containers.SettingPage.editSettings.title': '编辑视图（设置）',
        'content-manager.containers.SettingPage.layout': '布局',
        'content-manager.containers.SettingPage.listSettings.description': '配置此集合类型的选项',
        'content-manager.containers.SettingPage.listSettings.title': '列表视图（设置）',
        'content-manager.containers.SettingPage.pluginHeaderDescription': '为此集合类型配置特定设置',
        'content-manager.containers.SettingPage.relations': '相关字段',
        'content-manager.containers.SettingPage.settings': '设置',
        'content-manager.containers.SettingPage.view': '视图',
        'content-manager.containers.SettingViewModel.pluginHeader.title': '内容管理器 — {name}',
        'content-manager.containers.SettingsPage.Block.contentType.description': '配置特定设置',
        'content-manager.containers.SettingsPage.Block.contentType.title': '集合类型',
        'content-manager.containers.SettingsPage.Block.generalSettings.description': '配置集合类型的默认选项',
        'content-manager.containers.SettingsPage.Block.generalSettings.title': '常规',
        'content-manager.containers.SettingsPage.pluginHeaderDescription': '配置所有集合类型和组的设置',
        'content-manager.containers.SettingsView.list.subtitle': '配置集合类型和组的布局和显示',
        'content-manager.containers.SettingsView.list.title': '显示配置',
        'content-manager.containers.untitled': '无标题',
        'content-manager.dnd.cancel-item': '{item}, 已放置。重新排序已取消。',
        'content-manager.dnd.drop-item': '{item}, 已放置。最终列表位置：{position}。',
        'content-manager.dnd.grab-item': '{item}, 已抓取。当前列表位置：{position}。按上下箭头键更改位置，按空格键放置，按 Esc 取消。',
        'content-manager.dnd.instructions': '按空格键抓取并重新排序',
        'content-manager.dnd.reorder': '{item}, 已移动。新列表位置：{position}。',
        'content-manager.edit-settings-view.link-to-ctb.components': '编辑组件',
        'content-manager.edit-settings-view.link-to-ctb.content-types': '编辑内容类型',
        'content-manager.emptyAttributes.button': '转到集合类型构建器',
        'content-manager.emptyAttributes.description': '为您的集合类型添加第一个字段',
        'content-manager.emptyAttributes.title': '尚未有字段',
        'content-manager.error.attribute.key.taken': '此值已存在',
        'content-manager.error.attribute.sameKeyAndName': '不能相等',
        'content-manager.error.attribute.taken': '此字段名称已存在',
        'content-manager.error.contentTypeName.taken': '此名称已存在',
        'content-manager.error.model.fetch': '获取模型配置时发生错误。',
        'content-manager.error.record.create': '创建记录时发生错误。',
        'content-manager.error.record.delete': '删除记录时发生错误。',
        'content-manager.error.record.fetch': '获取记录时发生错误。',
        'content-manager.error.record.update': '更新记录时发生错误。',
        'content-manager.error.records.count': '获取记录计数时发生错误。',
        'content-manager.error.records.fetch': '获取记录时发生错误。',
        'content-manager.error.records.fetch-draft-relatons': '获取此文档的草稿关系时发生错误。',
        'content-manager.error.schema.generation': '生成架构时发生错误。',
        'content-manager.error.validation.json': '这不是一个有效的 JSON',
        'content-manager.error.validation.max': '值太高（最大值：{max}）。',
        'content-manager.error.validation.maxLength': '值太长（最大值：{max}）。',
        'content-manager.error.validation.min': '值太低（最小值：{min}）。',
        'content-manager.error.validation.minLength': '值太短（最小值：{min}）。',
        'content-manager.error.validation.minSupMax': '不能大于',
        'content-manager.error.validation.regex': '值不符合正则表达式。',
        'content-manager.error.validation.required': '此值输入是必需的。',
        'content-manager.form.Input.bulkActions': '启用批量操作',
        'content-manager.form.Input.defaultSort': '默认排序属性',
        'content-manager.form.Input.description': '描述',
        'content-manager.form.Input.description.placeholder': '在个人资料中显示的名称',
        'content-manager.form.Input.editable': '可编辑字段',
        'content-manager.form.Input.filters': '启用筛选器',
        'content-manager.form.Input.hint.character.unit': '{maxValue, plural, one { 个字符} other { 个字符}}',
        'content-manager.form.Input.hint.minMaxDivider': ' / ',
        'content-manager.form.Input.hint.text': '{min, select, undefined {} other {最小值. {min}}}{divider}{max, select, undefined {} other {最大值. {max}}}{unit}{br}{description}',
        'content-manager.form.Input.label': '标签',
        'content-manager.form.Input.label.inputDescription': '此值将覆盖表头中显示的标签',
        'content-manager.form.Input.pageEntries': '每页条目数',
        'content-manager.form.Input.pageEntries.inputDescription': '注意：您可以在集合类型设置页面中覆盖此值。',
        'content-manager.form.Input.placeholder': '占位符',
        'content-manager.form.Input.placeholder.placeholder': '我的超赞值',
        'content-manager.form.Input.search': '启用搜索',
        'content-manager.form.Input.search.field': '在此字段上启用搜索',
        'content-manager.form.Input.sort.field': '在此字段上启用排序',
        'content-manager.form.Input.sort.order': '默认排序顺序',
        'content-manager.form.Input.wysiwyg': '以 WYSIWYG 显示',
        'content-manager.global.displayedFields': '显示的字段',
        'content-manager.groups': '组',
        'content-manager.groups.numbered': '组 ({number})',
        'content-manager.header.name': '内容管理器',
        'content-manager.link-to-ctb': '编辑模型',
        'content-manager.models': '集合类型',
        'content-manager.models.numbered': '集合类型 ({number})',
        'content-manager.notification.error.displayedFields': '您需要至少一个显示字段',
        'content-manager.notification.error.relationship.fetch': '获取关系时发生错误。',
        'content-manager.notification.info.SettingPage.disableSort': '您需要有一个允许排序的属性',
        'content-manager.notification.info.minimumFields': '您需要至少有一个显示的字段',
        'content-manager.notification.upload.error': '上传文件时发生错误',
        'content-manager.pageNotFound': '页面未找到',
        'content-manager.pages.ListView.header-subtitle': '找到 {number, plural, =0 {# 条目} one {# 条目} other {# 条目}}',
        'content-manager.pages.NoContentType.button': '创建您的第一个内容类型',
        'content-manager.pages.NoContentType.text': '您还没有任何内容，我们建议您创建第一个内容类型。',
        'content-manager.permissions.not-allowed.create': '您没有创建文档的权限',
        'content-manager.permissions.not-allowed.update': '您没有查看此文档的权限',
        'content-manager.plugin.description.long': '快速查看、编辑和删除数据库中的数据。',
        'content-manager.plugin.description.short': '快速查看、编辑和删除数据库中的数据。',
        'content-manager.popUpWarning.bodyMessage.contentType.delete': '您确定要删除内容类型吗？',
        'content-manager.popUpWarning.bodyMessage.contentType.delete.all': '您确定要删除这些条目吗？',
        'content-manager.popUpWarning.bodyMessage.contentType.publish.all': '您确定要发布这些条目吗？',
        'content-manager.popUpWarning.bodyMessage.contentType.unpublish.all': '您确定要取消发布这些条目吗？',
        'content-manager.popUpWarning.warning.has-draft-relations.title': '确认',
        'content-manager.popUpWarning.warning.has-draft-relations.message': '此条目与 {count, plural, one {# 个草稿条目} other {# 个草稿条目}} 相关。发布它可能会在您的应用程序中留下断链。',
        'content-manager.popUpwarning.warning.has-draft-relations.button-confirm': '是，发布',
        'content-manager.popUpwarning.warning.bulk-has-draft-relations.message': '<b>{count} {count, plural, one { 关系 } other { 关系 } } 在 {entities} { entities, plural, one { 条目 } other { 条目 } } 中 {count, plural, one { 是 } other { 是 } }</b> 尚未发布，可能会导致意外行为。',
        'content-manager.popUpWarning.warning.publish-question': '您仍然要发布吗？',
        'content-manager.popUpWarning.warning.unpublish': '如果您不发布此内容，它将自动变为草稿。',
        'content-manager.popUpWarning.warning.unpublish-question': '您确定不发布它吗？',
        'content-manager.popUpWarning.warning.updateAllSettings': '这将修改所有您的设置',
        'content-manager.popover.display-relations.label': '显示关系',
        'content-manager.preview.panel.title': '预览',
        'content-manager.preview.panel.button': '打开预览',
        'content-manager.preview.panel.button-disabled-tooltip': '请保存以打开预览',
        'content-manager.preview.page-title': '{contentType} 预览',
        'content-manager.preview.header.close': '关闭预览',
        'content-manager.preview.copy.label': '复制预览链接',
        'content-manager.preview.copy.success': '已复制预览链接',
        'content-manager.preview.tabs.label': '预览状态',
        'content-manager.relation.add': '添加关系',
        'content-manager.relation.disconnect': '移除',
        'content-manager.relation.error-adding-relation': '添加关系时发生错误。',
        'content-manager.relation.isLoading': '关系正在加载',
        'content-manager.relation.loadMore': '加载更多',
        'content-manager.relation.notAvailable': '没有可用的关系',
        'content-manager.relation.publicationState.draft': '草稿',
        'content-manager.relation.publicationState.published': '已发布',
        'content-manager.reviewWorkflows.stage.label': '审查阶段',
        'content-manager.select.currently.selected': '当前选中 {count}',
        'content-manager.success.record.clone': '已克隆文档',
        'content-manager.success.record.discard': '已放弃更改',
        'content-manager.success.record.delete': '已删除文档',
        'content-manager.success.record.publish': '已发布文档',
        'content-manager.success.record.publishing': '正在发布...',
        'content-manager.success.record.save': '已保存文档',
        'content-manager.success.record.unpublish': '已取消发布文档',
        'content-manager.success.records.delete': '成功删除。',
        'content-manager.success.records.unpublish': '成功取消发布。',
        'content-manager.success.records.publish': '成功发布。',
        'content-manager.utils.data-loaded': '已成功加载 {number, plural, =1 {条目} other {条目}}',
        'content-manager.listView.validation.errors.title': '需要操作',
        'content-manager.listView.validation.errors.message': '请确保在发布之前所有字段都有效（必填字段、最小/最大字符限制等）',
        'content-manager.history.document-action': '内容历史',
        'content-manager.history.page-title': '{contentType} 历史',
        'content-manager.history.sidebar.title': '版本',
        'content-manager.history.sidebar.version-card.aria-label': '版本卡片',
        'content-manager.history.sidebar.versionDescription': '{distanceToNow}{isAnonymous, select, true {} other { 由 {author}}}{isCurrent, select, true { <b>(当前)</b>} other {}}',
        'content-manager.history.sidebar.show-newer': '显示较新版本',
        'content-manager.history.sidebar.show-older': '显示较旧版本',
        'content-manager.history.version.subtitle': '{hasLocale, select, true {{subtitle}, 在 {locale}} other {{subtitle}}}',
        'content-manager.history.content.new-field.title': '新字段',
        'content-manager.history.content.new-field.message': '保存此版本时，此字段不存在。如果您恢复此版本，它将是空的。',
        'content-manager.history.content.unknown-fields.title': '未知字段',
        'content-manager.history.content.unknown-fields.message': '这些字段已在内容类型构建器中删除或重命名。<b>这些字段将不会被恢复。</b>',
        'content-manager.history.content.missing-assets.title': '{number, plural, =1 {丢失的资产} other {{number} 个丢失的资产}}',
        'content-manager.history.content.missing-assets.message': '{number, plural, =1 {它} other {它们}} 已在媒体库中删除，无法恢复。',
        'content-manager.history.content.missing-relations.title': '{number, plural, =1 {丢失的关系} other {{number} 个丢失的关系}}',
        'content-manager.history.content.missing-relations.message': '{number, plural, =1 {它} other {它们}} 已被删除，无法恢复。',
        'content-manager.history.content.no-relations': '没有关系。',
        'content-manager.history.content.localized': '此值特定于此语言。如果您恢复此版本，内容将不会替换其他语言的内容。',
        'content-manager.history.content.not-localized': '此值对所有语言通用。如果您恢复此版本，内容将替换所有语言的内容。',
        'content-manager.history.restore.confirm.button': '恢复',
        'content-manager.history.restore.confirm.title': '您确定要恢复此版本吗？',
        'content-manager.history.restore.confirm.message': '{isDraft, select, true {恢复的内容将覆盖您的草稿。} other {恢复的内容不会发布，它将覆盖草稿并保存为待更改。您可以随时发布更改。}}',
        'content-manager.history.restore.success.title': '版本已恢复。',
        'content-manager.history.restore.success.message': '已恢复内容的一个旧版本。',
        'content-manager.history.restore.error.message': '无法恢复版本。',
        'content-manager.validation.error': '您的文档中有验证错误。请在保存之前修复它们。',
        'content-manager.bulk-publish.edit': '编辑',
        'content-manager.widget.last-edited.title': '最近编辑的条目',
        'content-manager.widget.last-edited.single-type': '单个类型',
        'content-manager.widget.last-edited.no-data': '没有编辑的条目',
        'content-manager.widget.last-published.title': '最近发布的条目',
        'content-manager.widget.last-published.no-data': '没有发布的条目',
        // 首页
        'HomePage.header.title': '你好，{name}',
        'HomePage.header.subtitle': '欢迎来到管理面板，你可以通过左侧菜单内容管理查看和管理数据。',
        'HomePage.widget.loading': '加载中',
        'HomePage.widget.error': '加载失败',
        'HomePage.widget.no-data': '没有内容',
      },
      'en': {
        'global.password': '密码',
        'Auth.components.Oops.text': '您的账户已被暂停。',
        'Auth.components.Oops.text.admin': '如果这是误操作，请联系您的管理员。',
        'Auth.components.Oops.title': '哎呀...',
        'Auth.form.active.label': '活跃',
        'Auth.form.button.forgot-password': '发送邮件',
        'Auth.form.button.go-home': '返回首页',
        'Auth.form.button.login': '登录',
        'Auth.form.button.login.providers.error': '无法通过所选提供商连接您。',
        'Auth.form.button.login.strapi': '通过 Strapi 登录',
        'Auth.form.button.password-recovery': '密码恢复',
        'Auth.form.button.register': '让我们开始',
        'Auth.form.confirmPassword.label': '确认密码',
        'Auth.form.currentPassword.label': '当前密码',
        'Auth.form.email.label': '邮箱',
        'Auth.form.email.placeholder': '请输入邮箱',
        'Auth.form.error.blocked': '您的账户已被管理员封禁。',
        'Auth.form.error.code.provide': '提供的验证码不正确。',
        'Auth.form.error.confirmed': '您的账户邮箱尚未确认。',
        'Auth.form.error.email.invalid': '此邮箱无效。',
        'Auth.form.error.email.provide': '请输入您的用户名或邮箱。',
        'Auth.form.error.email.taken': '邮箱已被占用。',
        'Auth.form.error.invalid': '标识符或密码无效。',
        'Auth.form.error.params.provide': '提供的参数不正确。',
        'Auth.form.error.password.format': '您的密码不能包含超过三个 `$` 符号。',
        'Auth.form.error.password.local': '此用户从未设置本地密码，请通过账户创建时使用的提供商登录。',
        'Auth.form.error.password.matching': '两次输入的密码不一致。',
        'Auth.form.error.password.provide': '请输入您的密码。',
        'Auth.form.error.ratelimit': '尝试次数过多，请一分钟后再试。',
        'Auth.form.error.user.not-exist': '此邮箱不存在。',
        'Auth.form.error.username.taken': '用户名已被占用。',
        'Auth.form.firstname.label': '名',
        'Auth.form.firstname.placeholder': '请输入名',
        'Auth.form.forgot-password.email.label': '请输入您的邮箱',
        'Auth.form.forgot-password.email.label.success': '已成功发送邮件至',
        'Auth.form.lastname.label': '姓',
        'Auth.form.lastname.placeholder': '请输入姓',
        'Auth.form.password.hide-password': '隐藏密码',
        'Auth.form.password.hint': '必须至少包含8个字符，1个大写字母，1个小写字母和1个数字',
        'Auth.form.password.show-password': '显示密码',
        'Auth.form.register.news.label': '请告知我有关新功能和即将进行的改进（通过这样做，您接受 {terms} 和 {policy}）。',
        'Auth.form.register.subtitle': '凭据仅用于在 Strapi 中进行身份验证。所有保存的数据将存储在您的数据库中。',
        'Auth.form.rememberMe.label': '记住我',
        'Auth.form.username.label': '用户名',
        'Auth.form.username.placeholder': '请输入用户名',
        'Auth.form.welcome.subtitle': '登录您的账户',
        'Auth.form.welcome.title': '欢迎使用管理端',
        'Auth.link.forgot-password': '忘记密码？',
        'Auth.link.ready': '准备登录？',
        'Auth.link.signin': '登录',
        'Auth.link.signin.account': '已有账户？',
        'Auth.login.sso.divider': '或通过以下方式登录',
        'Auth.login.sso.loading': '正在加载提供商...',
        'Auth.login.sso.subtitle': '通过 SSO 登录到您的账户',
        'Auth.privacy-policy-agreement.policy': '隐私政策',
        'Auth.privacy-policy-agreement.terms': '服务条款',
        'Auth.reset-password.title': '重置密码',
      },
    },
    head: {
      favicon: Logo,
      title: '文献管理系统',
    },
    auth: {
      logo: Logo,
    },
    menu: {
      logo: Logo,
    },
    tutorials: false,
    notifications: { releases: false },
  },
  bootstrap(app) {
    app.getPlugin('content-manager').injectComponent('listView', 'actions', {
      name: 'UploadButton',
      Component: UploadButton,
    })
    app.getPlugin('content-manager').injectComponent('listView', 'actions', {
      name: 'DownloadButton',
      Component: DownloadButton,
    })

    app.getPlugin('content-manager').injectComponent('listView', 'actions', {
      name: 'NormaliseButton',
      Component: NormaliseButton,
    })
    // Function to check if current user is admin and hide settings link for non-admins
    const checkUserAndHideSettings = async () => {
      try {
        // Get the current user info from Strapi admin
        const res = await get('/admin/users/me')

        // Check if the user is a super admin
        const isAdmin = res.data?.data?.roles?.some(role => role.code === 'strapi-super-admin')

        // Only proceed if user is not an admin
        if (!isAdmin) {
          // Use MutationObserver to watch for DOM changes and hide settings link when it appears
          const hideSettingsLink = () => {
            const settingsLink = document.querySelector('[href="/admin/settings"]')
            if (settingsLink) {
              settingsLink.style.display = 'none'
              return true // Found and hidden
            }
            return false // Not found yet
          }

          // Try to hide immediately if already rendered
          if (!hideSettingsLink()) {
            // If not found, use MutationObserver to watch for DOM changes
            const observer = new MutationObserver((mutations) => {
              mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                  // Check if settings link was added
                  if (hideSettingsLink()) {
                    // Successfully hidden, can stop observing
                    observer.disconnect()
                  }
                }
              })
            })

            // Start observing the document body for changes
            observer.observe(document.body, {
              childList: true,
              subtree: true,
            })

            // Fallback: stop observing after 30 seconds to prevent memory leaks
            setTimeout(() => {
              observer.disconnect()
            }, 30000)
          }
        }
      }
      catch (error) {
        console.error('Error checking user role:', error)
      }
    }

    // Start the process
    checkUserAndHideSettings()
  },
}
