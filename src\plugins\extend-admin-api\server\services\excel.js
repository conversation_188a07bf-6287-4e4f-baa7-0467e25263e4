'use strict'
const { errors } = require('@strapi/utils')
const excelJS = require('exceljs')
const lodash = require('lodash')
const uuid = require('uuid')
const xlsx = require('xlsx')

const { ApplicationError } = errors

/**
 * excel service
 */

module.exports = {

  /**
   * 创建excel上传记录
   * @param file
   * @param type
   * @param createCount
   * @param updateCount
   * @param userID
   * @returns {Promise<Result<string, {data: {file, update_count, type, create_count}}>>}
   */
  async createExcelLog(file, type, createCount, updateCount, userID) {
    const fileResult = await strapi.plugins.upload.services.upload.upload({
      data: {
        fileInfo: {
          name: `${uuid.v7()}.xlsx`,
        },
      },
      files: file,
    })
    return strapi.documents('api::excel.excel').create({
      data: {
        file: fileResult[0].id,
        type,
        createdBy: userID,
        create_count: createCount,
        update_count: updateCount,
      },
    })
    // return strapi.entityService.create('plugin::extend-admin-api.excel', {
    //   data: {
    //     file: fileResult[0].id,
    //     type,
    //     create_count: createCount,
    //     update_count: updateCount,
    //   },
    // })
  },

  /**
   * 下载excel
   * @param uid
   * @param contentType
   * @param query
   * @param rules
   * @returns {Promise<any>}
   */
  async download({ uid, contentType, query, rules }) {
    // 查询总页数
    query.pagination = {
      withCount: true,
    }
    const sanitizedQueryParams = await strapi.contentAPI.sanitize.query(
      query,
      contentType,
    )
    const totalCount = await strapi.documents(uid).count(sanitizedQueryParams)

    // 获取excel表头数据（中文名称）
    const rowHeader = {}
    Object.keys(rules.attributes).forEach((key) => {
      if (rules.attributes[key].extIsFieldExported !== false) {
        rowHeader[key] = rules.attributes[key].extExcelTitle
      }
    })

    // 创建一个Excel工作簿（流模式）
    const fileName = `${uuid.v7()}.xlsx`
    const workbook = new excelJS.stream.xlsx.WorkbookWriter({
      filename: `${process.cwd()}/public/${fileName}`,
    })
    const worksheet = workbook.addWorksheet('sheet1')

    // 添加表头
    worksheet.addRow(Object.values(rowHeader)).commit()

    // 递归查询数据并插入excel
    const pageSize = 10000
    const totalPage = Math.ceil(totalCount / pageSize)
    let page = 0
    const recursiveWriteExcelData = async () => {
      if (page < totalPage) {
        page = page + 1
        const queryParams = query
        queryParams.pageSize = pageSize
        queryParams.page = page
        // const sanitizedQueryParams = await strapi.contentAPI.sanitize.query(
        //   query,
        //   contentType,
        // )
        const rows = await strapi.documents(uid).findMany({
          // ...sanitizedQueryParams
          start: (page - 1) * pageSize,
          limit: pageSize,
        })
        for (const row of rows) {
          worksheet.addRow(Object.keys(rowHeader).map(key => row[key] ? row[key] : '')).commit()
        }
        await recursiveWriteExcelData()
      }
    }
    await recursiveWriteExcelData()
    await workbook.commit()
    return `${process.cwd()}/public/${fileName}`
  },

  /**
   * excel上传（导入）
   * @param filePath
   * @param type
   * @param userID
   * @returns {Promise<{updateResult: *[], insertResult: *[]}>}
   */
  async upload(filePath, type, userID) {
    const contentTypes = strapi.plugin('extend-admin-api').service('common').getContentTypes(type)
    if (!contentTypes) {
      throw new ApplicationError('不支持此类型')
    }
    const excelRulesResult = await this.getValidatedExcelData({ filePath, rules: contentTypes.attributes })
    let excelData = Object.assign([], excelRulesResult)
    const insertData = []
    const updateData = []
    if (!excelData || excelData.length === 0) {
      return {
        data: {
          insertResult: [],
          updateResult: [],
        },
      }
    }
    const extUniqueField = strapi.plugin('extend-admin-api').service('common').getContentTypes(type).extUniqueField
    excelData = excelData.map((v) => {
      Object.keys(v).forEach((key) => {
        if (v[key].value !== undefined) {
          if (v[key].rules && v[key].rules.type === 'text') {
            v[key] = v[key].value.toString()
          }
          else if (v[key].value === '') {
            v[key] = null
          }
          else {
            v[key] = v[key].value
          }
        }
      })
      return v
    })

    // 1.通过唯一字段查询数据数据
    // 未存在的数据进行插入，标准化数据为name或者code是唯一标识、文献数据则是identifier

    const findRelatedFieldWhere = extUniqueField.map(extUniqueFieldKey => ({
      [extUniqueFieldKey]: {
        $in: excelData.map(v => v[extUniqueFieldKey]),
      },
    }))
    let relatedFieldResult = await strapi.documents(`api::${type}.${type}`).findMany({
      filters: {
        $or: findRelatedFieldWhere,
      },
    })
    // 过滤出不存在的数据
    excelData.forEach((v) => {
      const obj = Object.assign({}, v)
      const item = relatedFieldResult.find((v2) => {
        const items = extUniqueField.find(key => v2[key] === obj[key])
        if (items) {
          return true
        }
        return false
      })
      if (!item) {
        updateData.push(lodash.cloneDeep(obj))
        delete obj.parent
        insertData.push(obj)
      }
      else {
        updateData.push(obj)
      }
    })
    // 用于首次插入时的过滤
    const insertDataFilter = lodash.cloneDeep(insertData)
    const insertResults = []
    // 插入不存在的数据
    if (insertData.length > 0) {
      const errorLogs = []

      let lineNumber = 1
      const insertDataFunction = async () => {
        if (insertData.length > 0) {
          const data = insertData.shift()
          lineNumber++
          if (data) {
            try {
              data.dataSource = 'excel'
              insertResults.push(await strapi.documents(`api::${type}.${type}`).create({
                data: {
                  ...data,
                  createdBy: userID,
                },
              }))
            }
            catch (e) {
              if (e && e.details) {
                if (e.details.errors && e.details.errors.length > 0) {
                  e.details.errors.forEach((v) => {
                    errorLogs.push({
                      行号: lineNumber,
                      字段名称: v.path.map(v => strapi.plugin('extend-admin-api').service('common').getContentTypes(type).attributes[v] && strapi.plugin('extend-admin-api').service('common').getContentTypes(type).attributes[v].extExcelTitle ? strapi.plugin('extend-admin-api').service('common').getContentTypes(type).attributes[v].extExcelTitle : null).toString(),
                      字段编码: v.path.toString(),
                      字段值: v.message,
                    })
                  })
                }
                else {
                  errorLogs.push({
                    行号: lineNumber,
                    字段值: e.message,
                  })
                }
              }
              else {
                errorLogs.push({
                  行号: lineNumber,
                  字段值: e.message,
                })
              }
            }
          }
          await insertDataFunction()
        }
      }
      await insertDataFunction()
      await this.exportExcelErrorData(errorLogs)
    }
    // 2.查询出所有的name或者其他可关联字段数据，与导入的数据进行比对
    relatedFieldResult = await strapi.documents(`api::${type}.${type}`).findMany({
      filters: {
        $or: findRelatedFieldWhere,
      },
      populate: '*',
    })

    let recursionUpdateLog = []
    const errorLogs = []

    // 修改数据
    let lineNumber = 1
    const updateDataRecursion = async () => {
      if (updateData.length > 0) {
        lineNumber++
        const enumItem = Object.assign({}, updateData.shift())

        if (enumItem.parent) {
          extUniqueField.forEach((key) => {
            // excel表格中的父节点 可以录入code或者name进行关联
            const item = relatedFieldResult.find(v2 => v2[key] === enumItem.parent.toString())
            if (item) {
              enumItem.parent = { connect: [item.id] }
            }
          })
          if (enumItem.parent && !enumItem.parent.connect) {
            return errorLogs.push({
              行号: lineNumber,
              字段名称: '父节点',
              字段编码: 'parent',
              字段值: enumItem.parent,
            })
          }
        }
        else if (enumItem.parent === '') {
          enumItem.parent = { set: [] }
        }
        try {
          const findData = relatedFieldResult.find((v) => {
            const item = extUniqueField.find(key => enumItem[key] && v[key] === enumItem[key])
            if (item) {
              return true
            }
            return false
          })
          // 数据存在进行修改
          if (findData && findData.documentId) {
            enumItem.updateType = 'excelImport'
            enumItem.dataSource = 'excel'
            const updateResult = await strapi.documents(`api::${type}.${type}`).update({
              documentId: findData.documentId,
              data: {
                ...enumItem,
                updatedBy: userID,
              },
              populate: '*',
            })
            if (Object.keys(findData).find(key => !['parent', 'publishedAt', 'createdAt', 'updatedAt'].includes(key) && findData[key] !== updateResult[key])) {
              // 过滤插入的数据，如果是插入的数据则不统计进入修改的数组
              let items = []
              if (insertDataFilter.length > 0) {
                items = extUniqueField.map((extUniqueFieldKey) => {
                  return insertDataFilter.find(v => v[extUniqueFieldKey] === enumItem[extUniqueFieldKey])
                })
              }

              if (items.length === 0) {
                recursionUpdateLog.push(updateResult)
              }
            }
          }
        }
        catch (e) {
          if (e && e.message) {
            errorLogs.push({
              行号: lineNumber,
              字段名称: null,
              字段编码: null,
              字段值: e.message,
            })
          }
        }
        await updateDataRecursion()
      }
    }
    await updateDataRecursion()
    await this.exportExcelErrorData(errorLogs)
    // 标准化数据
    if (type === 'literature' || type === 'paper') {
      // 如果是文献、论文执行标准化数据
      // 根据唯一标识查询数据
      const list = await strapi.documents(`api::${type}.${type}`).findMany({
        filters: {
          identifier: {
            $in: excelData.map(v => v.identifier),
          },
        },
      })
      await strapi.plugin('extend-admin-api').service('common').formatData({ data: list, type })
    }
    // 针对同一条数据，多次修改。只保留最后一次的修改
    if (recursionUpdateLog && recursionUpdateLog.length > 0) {
      const recursionUpdateLogGroupBy = lodash.groupBy(recursionUpdateLog, 'id')
      const deduplicateData = []
      for (const key in recursionUpdateLogGroupBy) {
        deduplicateData.push(recursionUpdateLogGroupBy[key][recursionUpdateLogGroupBy[key].length - 1])
      }
      recursionUpdateLog = deduplicateData
    }
    return { insertResult: insertResults, updateResult: recursionUpdateLog }
  },

  /**
   * 生成excel数据
   * @param data
   * @param fileName
   * @returns {Promise<any>}
   */
  async writeData(data, fileName) {
    const workbook = xlsx.utils.book_new()
    const worksheet = xlsx.utils.json_to_sheet(data)

    // 将工作表添加到工作簿中
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
    return await xlsx.writeFile(workbook, fileName)
  },

  /**
   * 导出错误信息（excel格式）
   * @param errorLogs
   * @returns {Promise<void>}
   */
  async exportExcelErrorData(errorLogs) {
    if (errorLogs && errorLogs.length > 0) {
      const filePath = `/validation_errors/${uuid.v7()}.xlsx`
      await this.writeData(errorLogs, `${process.cwd()}/public${filePath}`)
      throw new ApplicationError('excel存在错误数据', { filePath })
    }
  },

  /**
   * 简单验证excel导入的数据
   * @param rowsMeta
   * @returns {Promise<*[]>}
   */
  async excelValidateData({ rowsMeta }) {
    let lineNumber = 1
    let errorLogs = []
    const validateDataValue = Object.assign([], rowsMeta)
    // 递归验证
    const processData = async () => {
      if (validateDataValue.length > 0) {
        lineNumber++
        const data = validateDataValue.shift()
        const validationResult = await Promise.all(Object.keys(data).map(async (key) => {
          // 必填匹配
          // if (data[key] && data[key].rules && data[key].rules.required && !data[key].value) {
          //   return {
          //     行号: lineNumber,
          //     字段名称: data[key].rules.extExcelTitle,
          //     字段编码: data[key].eName,
          //     字段值: data[key].value,
          //   }
          // }
          // 唯一性校验
          // 数据校验仅保留单个文件中不允许存在重复数据
          if (data[key] && data[key].rules && data[key].rules.unique && data[key].value && rowsMeta.filter(v => v[key].value === data[key].value).length > 1) {
            return {
              行号: lineNumber,
              字段名称: data[key].rules.extExcelTitle,
              字段编码: data[key].eName,
              字段值: data[key].value,
              // message: `${data[key].cName}不可为重复项，重复值为:${data[key].value}`,
            }
          }
          // 正则匹配
          // if (data[key].value && data[key].rules && data[key].rules.format && data[key].rules.format.test(data[key].value) === false) {
          //   return {
          //     行号: lineNumber,
          //     字段名称: data[key].rules.displayName,
          //     字段编码: data[key].eName,
          //     字段值: data[key].value,
          //     // line: lineNumber,
          //     // message: `${data[key].cName}不符合规则`,
          //   }
          // }
          // enum匹配
          // if (data[key].value && data[key].rules && data[key].rules.model && data[key].rules.model.name) {
          //   data[key].value = data[key].value.toString()
          //   const contentArray = []
          //   data[key].value.split(data[key].rules.model.separator).filter(v => v).forEach((v) => {
          //     data[key].rules.model.attributes.forEach((v2) => {
          //       contentArray.push({
          //         [v2]: v,
          //       })
          //     })
          //   })
          //   const enumValueResult = await enumValue(`api::${data[key].rules.model.name}.${data[key].rules.model.name}`, contentArray)
          //   if (!enumValueResult || enumValueResult.length === 0 || data[key].value.split(data[key].rules.model.separator).filter(v => v).length !== enumValueResult.length) {
          //     return {
          //       行号: lineNumber,
          //       字段名称: data[key].rules.displayName,
          //       字段编码: data[key].eName,
          //       字段值: data[key].value,
          //
          //       // line: lineNumber,
          //       // message: `${data[key].cName}的值不在范围内`,
          //     }
          //   }
          //   else {
          //     rowsMeta[lineNumber - 2][key].value = {
          //       connect: enumValueResult.map(v => v.id),
          //     }
          //     // data[key].value = {
          //     //   connect: enumValueResult.map(v => v.id),
          //     // }
          //   }
          // }
          // 当关联类型并且数据不存在时返回空数组
          /* if (!data[key].value && data[key].rules && data[key].rules.model && data[key].rules.model.name) {
           rowsMeta[lineNumber - 2][key].value = {
           connect: [],
           }
           } */
          // file匹配
          // if (data[key].value && data[key].rules && data[key].rules.file && data[key].rules.file.required) {
          //   let md5Attr = null
          //   data[key].rules.file.attributes.forEach((v) => {
          //     md5Attr += data[v]
          //   })
          //
          //   const uniqueIdentifier = md5(md5Attr)
          //   let fileValidationResult = data[key].rules.file.fileType.map(async (v) => {
          //     let oldResult = null
          //     if (data[key].rules.file.old)
          //       oldResult = await ossClient.fileExist(`${data[data[key].rules.file.old]}.${v}`)
          //
          //     const uniqueIdentifierResult = await ossClient.fileExist(`${uniqueIdentifier}.${v}`)
          //     return {
          //       fileType: v,
          //       old: oldResult,
          //       uniqueIdentifier: uniqueIdentifierResult,
          //     }
          //   })
          //   fileValidationResult = await Promise.all(fileValidationResult)
          //   fileValidationResult = fileValidationResult.filter(v => (v.old && v.old.message === 'success') || (v.uniqueIdentifier && v.uniqueIdentifier.message === 'success'))
          //   if (fileValidationResult.length !== 2) {
          //     return {
          //       line: lineNumber,
          //       message: `pdf或者png文件不存在`,
          //     }
          //   }
          // }
          return null
        }))
        errorLogs = errorLogs.concat(validationResult.filter(v => v))
        await processData()
      }
    }
    await processData()

    return errorLogs
  },

  /**
   * 根据excel首行中文字段名称，获取规则
   * @param rows
   * @param rules
   * @returns {Promise<*>}
   */
  async getHeaderRules({ rows, rules }) {
    // 中文字段转换成英文规则
    return rows.shift().map((key, index) => {
      const obj = {}
      Object.keys(rules).forEach((v) => {
        if (rules[v].extExcelTitle === key) {
          obj.eName = v
          // obj.cName = key
          obj.rules = rules[v]
          obj.index = index
        }
      })
      if (Object.keys(obj).length === 0) {
        return {}
      }
      return obj
    }).filter(v => v)
  },

  /**
   * 读取excel并获取数据
   * @param filePath
   * @returns {Promise<unknown[]>}
   */
  async getExcelData({ filePath }) {
    const workbook = xlsx.readFile(filePath)

    const sheet = workbook.Sheets[workbook.SheetNames[0]]
    if (!sheet['!ref']) {
      throw new ApplicationError('excel存在错误数据', {
        data: [{
          message: `excel内容不可为空`,
        }],
      })
    }
    const range = xlsx.utils.decode_range(sheet['!ref'])
    for (let r = range.s.r; r <= range.e.r; r++) {
      for (let c = range.s.c; c <= range.e.c; c++) {
        const cellAddress = xlsx.utils.encode_cell({ r, c })
        if (!sheet[cellAddress]) {
          sheet[cellAddress] = { t: 's', v: '' }
        }
      }
    }
    return xlsx.utils.sheet_to_json(sheet, { header: 1 })
  },

  /**
   * 获取校验后的excel数据
   * @param filePath
   * @param rules
   * @returns {Promise<void>}
   */
  async getValidatedExcelData({ filePath, rules }) {
    const rulesObj = {}
    Object.keys(rules).forEach((key) => {
      if (rules[key] && rules[key].isValidationRequired !== false) {
        rulesObj[key] = rules[key]
      }
    })
    // 获取原始excel数据
    const rows = await this.getExcelData({ filePath })
    // 处理头部标题，根据headers内容获取规则
    const headers = await this.getHeaderRules({ rows, rules: rulesObj })

    // 按照类型处理原始数据并结合规则
    let rowsMeta = Object.assign([], rows)
    rowsMeta = rowsMeta.map((v) => {
      const obj = {}
      v = v.splice(0, headers.length)
      v.forEach((v2, i) => {
        if (headers[i] && headers[i].rules && headers[i].rules.type === 'string') {
          obj[headers[i].eName] = Object.assign({ value: `${v2}` }, headers[i])
        }
        else if (headers[i]) {
          obj[headers[i].eName] = Object.assign({ value: v2 }, headers[i])
        }
      })
      return obj
    })

    // 校验数据是否符合规则
    const errorLogs = await this.excelValidateData({ rowsMeta })
    if (errorLogs && errorLogs.length > 0) {
      const filePath = `/validation_errors/${uuid.v7()}.xlsx`
      await this.writeData(errorLogs, `${process.cwd()}/public${filePath}`)
      throw new ApplicationError('excel存在错误数据', { filePath })
    }
    // 返回数据
    return rowsMeta
  },
}
