'use strict'
const { createCoreController } = require('@strapi/strapi').factories

/**
 * literature controller
 */

module.exports = createCoreController('api::literature.literature', ({ strapi }) => ({
  /**
   * api创建文献
   * @param ctx
   * @returns {Promise<void>}
   */
  async apiCreate(ctx) {
    let userID = null
    if (ctx.state && ctx.state.user && ctx.state.user.id !== undefined) {
      userID = ctx.state.user.id
    }
    ctx.body = await strapi.service(`api::literature.literature`).apiCreate(ctx.request.body, userID)
  },
}))
