services:
  facisp-document-api:
    image: node:22
    container_name: facisp-document-api
    working_dir: /opt/project/facisp-document-api
    restart: always
    env_file:
      - .env
    ports:
      - "0.0.0.0:1337:1337"
    volumes:
      - /opt/project/facisp-document-api:/opt/project/facisp-document-api
    command: >
      sh -c "npm ci --sharp_binary_host="https://npmmirror.com/mirrors/sharp" --sharp_libvips_binary_host="https://npmmirror.com/mirrors/sharp-libvips" --registry=https://registry.npmmirror.com && npm run start"
    networks:
      - shared_network
networks:
  shared_network:
    external: true

