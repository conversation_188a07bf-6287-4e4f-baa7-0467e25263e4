import type { Schema, Struct } from '@strapi/strapi';

export interface AdminApiToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_tokens';
  info: {
    description: '';
    displayName: 'Api Token';
    name: 'Api Token';
    pluralName: 'api-tokens';
    singularName: 'api-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::api-token'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'read-only'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_token_permissions';
  info: {
    description: '';
    displayName: 'API Token Permission';
    name: 'API Token Permission';
    pluralName: 'api-token-permissions';
    singularName: 'api-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::api-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
  collectionName: 'admin_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'Permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::permission'> &
      Schema.Attribute.Private;
    properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<'manyToOne', 'admin::role'>;
    subject: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminRole extends Struct.CollectionTypeSchema {
  collectionName: 'admin_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'Role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::role'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>;
  };
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_tokens';
  info: {
    description: '';
    displayName: 'Transfer Token';
    name: 'Transfer Token';
    pluralName: 'transfer-tokens';
    singularName: 'transfer-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminTransferTokenPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    description: '';
    displayName: 'Transfer Token Permission';
    name: 'Transfer Token Permission';
    pluralName: 'transfer-token-permissions';
    singularName: 'transfer-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::transfer-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminUser extends Struct.CollectionTypeSchema {
  collectionName: 'admin_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'User';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    blocked: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    isActive: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    lastname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::user'> &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    preferedLanguage: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    roles: Schema.Attribute.Relation<'manyToMany', 'admin::role'> &
      Schema.Attribute.Private;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String;
  };
}

export interface ApiBookBook extends Struct.CollectionTypeSchema {
  collectionName: 'books';
  info: {
    displayName: '\u591A\u4E1A\u52A1\u5E93/OA\u56FE\u4E66';
    pluralName: 'books';
    singularName: 'book';
  };
  options: {
    comment: '';
    draftAndPublish: false;
  };
  attributes: {
    abstract: Schema.Attribute.Text;
    article_trans_abstract: Schema.Attribute.Text;
    article_trans_subtitle: Schema.Attribute.Text;
    article_trans_title: Schema.Attribute.Text;
    article_type: Schema.Attribute.String;
    author: Schema.Attribute.Text;
    author_org: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    database_id: Schema.Attribute.String;
    detailurl: Schema.Attribute.Text;
    doi: Schema.Attribute.String;
    edition: Schema.Attribute.String;
    end_page: Schema.Attribute.String;
    file_path_url: Schema.Attribute.Text;
    files: Schema.Attribute.String;
    files_normal: Schema.Attribute.Text;
    full_name: Schema.Attribute.String;
    identifier: Schema.Attribute.String;
    institution: Schema.Attribute.String;
    institution_id: Schema.Attribute.String;
    keyword: Schema.Attribute.Text;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::book.book'> &
      Schema.Attribute.Private;
    oa_id: Schema.Attribute.String;
    page_info: Schema.Attribute.String;
    page_num: Schema.Attribute.String;
    process_date: Schema.Attribute.String;
    process_level: Schema.Attribute.String;
    process_mode: Schema.Attribute.Text;
    publishedAt: Schema.Attribute.DateTime;
    source_id: Schema.Attribute.String;
    source_isbn: Schema.Attribute.Text;
    source_title: Schema.Attribute.Text;
    start_page: Schema.Attribute.String;
    subtitle: Schema.Attribute.Text;
    t_num: Schema.Attribute.String;
    title: Schema.Attribute.Text;
    trans_keyword: Schema.Attribute.String;
    trans_language: Schema.Attribute.String;
    unique_md5: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBulletinBulletin extends Struct.CollectionTypeSchema {
  collectionName: 'bulletins';
  info: {
    displayName: '\u591A\u4E1A\u52A1\u5E93/Bulletin';
    pluralName: 'bulletins';
    singularName: 'bulletin';
  };
  options: {
    comment: '';
    draftAndPublish: false;
  };
  attributes: {
    author_org: Schema.Attribute.String;
    collection_name: Schema.Attribute.String;
    country: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    detailurl: Schema.Attribute.Text;
    file_type: Schema.Attribute.String;
    files: Schema.Attribute.String;
    files_normal: Schema.Attribute.Text;
    identifier: Schema.Attribute.String;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bulletin.bulletin'
    > &
      Schema.Attribute.Private;
    pub_date: Schema.Attribute.String;
    pub_year: Schema.Attribute.String;
    publish_cycle: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    risk_rating: Schema.Attribute.String;
    source_name: Schema.Attribute.String;
    source_name_short: Schema.Attribute.String;
    source_name_zh: Schema.Attribute.String;
    source_type: Schema.Attribute.String;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
    topic: Schema.Attribute.String;
    unique_md5: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    volume: Schema.Attribute.String;
  };
}

export interface ApiConferenceConference extends Struct.CollectionTypeSchema {
  collectionName: 'conferences';
  info: {
    displayName: '\u591A\u4E1A\u52A1\u5E93/OA\u4F1A\u8BAE';
    pluralName: 'conferences';
    singularName: 'conference';
  };
  options: {
    comment: '';
    draftAndPublish: false;
  };
  attributes: {
    abstract: Schema.Attribute.Text;
    article_trans_abstract: Schema.Attribute.Text;
    article_trans_subtitle: Schema.Attribute.Text;
    article_trans_title: Schema.Attribute.Text;
    article_type: Schema.Attribute.String;
    auth_email: Schema.Attribute.Text;
    author: Schema.Attribute.Text;
    author_org: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    database_id: Schema.Attribute.String;
    detailurl: Schema.Attribute.Text;
    doi: Schema.Attribute.String;
    end_page: Schema.Attribute.String;
    file_path_url: Schema.Attribute.Text;
    files: Schema.Attribute.String;
    files_normal: Schema.Attribute.Text;
    full_name: Schema.Attribute.String;
    identifier: Schema.Attribute.String;
    institution: Schema.Attribute.String;
    institution_id: Schema.Attribute.String;
    keyword: Schema.Attribute.Text;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::conference.conference'
    > &
      Schema.Attribute.Private;
    oa_id: Schema.Attribute.String;
    page_info: Schema.Attribute.String;
    page_num: Schema.Attribute.String;
    process_date: Schema.Attribute.String;
    process_level: Schema.Attribute.String;
    process_mode: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    source_id: Schema.Attribute.String;
    source_issn: Schema.Attribute.Text;
    source_title: Schema.Attribute.Text;
    start_page: Schema.Attribute.String;
    subtitle: Schema.Attribute.Text;
    t_num: Schema.Attribute.String;
    title: Schema.Attribute.Text;
    trans_keyword: Schema.Attribute.String;
    trans_language: Schema.Attribute.String;
    unique_md5: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumCaasEnumCaas extends Struct.CollectionTypeSchema {
  collectionName: 'enum_caas';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-CAAS\u5B66\u79D1';
    pluralName: 'enum-caass';
    singularName: 'enum-caas';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-caas.enum-caas'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    parent: Schema.Attribute.Relation<'oneToOne', 'api::enum-caas.enum-caas'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumCountryEnumCountry extends Struct.CollectionTypeSchema {
  collectionName: 'enum_country';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u56FD\u5BB6';
    pluralName: 'enum-countries';
    singularName: 'enum-country';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-country.enum-country'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    parent: Schema.Attribute.Relation<
      'oneToOne',
      'api::enum-country.enum-country'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumLiteratureAccessTypeEnumLiteratureAccessType
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_literature_access_type';
  info: {
    description: '';
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u83B7\u53D6\u65B9\u5F0F';
    pluralName: 'enum-literature-access-types';
    singularName: 'enum-literature-access-type';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-literature-access-type.enum-literature-access-type'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumLiteratureCategoryEnumLiteratureCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_literature_categories';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u51FA\u7248\u5206\u7C7B';
    pluralName: 'enum-literature-categories';
    singularName: 'enum-literature-category';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-literature-category.enum-literature-category'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    parent: Schema.Attribute.Relation<
      'oneToOne',
      'api::enum-literature-category.enum-literature-category'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumLiteratureIndustryEnumLiteratureIndustry
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_literature_industries';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u884C\u4E1A\u7C7B\u522B';
    pluralName: 'enum-literature-industries';
    singularName: 'enum-literature-industry';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-literature-industry.enum-literature-industry'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    parent: Schema.Attribute.Relation<
      'oneToOne',
      'api::enum-literature-industry.enum-literature-industry'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumLiteratureLanguageEnumLiteratureLanguage
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_literature_languages';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u8BED\u79CD';
    pluralName: 'enum-literature-languages';
    singularName: 'enum-literature-language';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-literature-language.enum-literature-language'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumLiteraturePublishCycleEnumLiteraturePublishCycle
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_literature_publish_cycles';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u51FA\u7248\u5468\u671F';
    pluralName: 'enum-literature-publish-cycles';
    singularName: 'enum-literature-publish-cycle';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-literature-publish-cycle.enum-literature-publish-cycle'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumLiteratureRiskRatingEnumLiteratureRiskRating
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_literature_risk_ratings';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u98CE\u9669\u7B49\u7EA7';
    pluralName: 'enum-literature-risk-ratings';
    singularName: 'enum-literature-risk-rating';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-literature-risk-rating.enum-literature-risk-rating'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumLiteratureUseTypeEnumLiteratureUseType
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_literature_use_types';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u4F7F\u7528\u6743\u9650';
    pluralName: 'enum-literature-use-types';
    singularName: 'enum-literature-use-type';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-literature-use-type.enum-literature-use-type'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumPaperCallStringEnumPaperCallString
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_paper_call_strings';
  info: {
    displayName: '\u5B66\u4F4D\u8BBA\u6587\u5E93/\u89C4\u8303-\u6307\u5BFC\u8001\u5E08';
    pluralName: 'enum-paper-call-strings';
    singularName: 'enum-paper-call-string';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    identifier: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-paper-call-string.enum-paper-call-string'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    tutor_home_page: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    work_unit: Schema.Attribute.String;
  };
}

export interface ApiEnumPaperDegreeTypeEnumPaperDegreeType
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_paper_degree_types';
  info: {
    displayName: '\u5B66\u4F4D\u8BBA\u6587\u5E93/\u89C4\u8303-\u5B66\u4F4D\u7C7B\u578B';
    pluralName: 'enum-paper-degree-types';
    singularName: 'enum-paper-degree-type';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-paper-degree-type.enum-paper-degree-type'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumPaperDisciplineEnumPaperDiscipline
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_paper_disciplines';
  info: {
    displayName: '\u5B66\u4F4D\u8BBA\u6587\u5E93/\u89C4\u8303-\u5B66\u79D1';
    pluralName: 'enum-paper-disciplines';
    singularName: 'enum-paper-discipline';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-paper-discipline.enum-paper-discipline'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    parent: Schema.Attribute.Relation<
      'oneToOne',
      'api::enum-paper-discipline.enum-paper-discipline'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumPaperProfessionalFieldNavigationEnumPaperProfessionalFieldNavigation
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_paper_professional_field_navigations';
  info: {
    displayName: '\u5B66\u4F4D\u8BBA\u6587\u5E93/\u89C4\u8303-\u4E13\u4E1A\u9886\u57DF';
    pluralName: 'enum-paper-professional-field-navigations';
    singularName: 'enum-paper-professional-field-navigation';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-paper-professional-field-navigation.enum-paper-professional-field-navigation'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    parent: Schema.Attribute.Relation<
      'oneToOne',
      'api::enum-paper-professional-field-navigation.enum-paper-professional-field-navigation'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumPaperTrainingUnitEnumPaperTrainingUnit
  extends Struct.CollectionTypeSchema {
  collectionName: 'enum_paper_training_units';
  info: {
    displayName: '\u5B66\u4F4D\u8BBA\u6587\u5E93/\u89C4\u8303-\u57F9\u517B\u5355\u4F4D';
    pluralName: 'enum-paper-training-units';
    singularName: 'enum-paper-training-unit';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-paper-training-unit.enum-paper-training-unit'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name1: Schema.Attribute.String;
    name2: Schema.Attribute.String;
    name3: Schema.Attribute.String;
    name4: Schema.Attribute.String;
    name5: Schema.Attribute.String;
    name6: Schema.Attribute.String;
    name7: Schema.Attribute.String;
    name8: Schema.Attribute.String;
    name9: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEnumSourceEnumSource extends Struct.CollectionTypeSchema {
  collectionName: 'enum_sources';
  info: {
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u89C4\u8303-\u6765\u6E90\u7F51\u7AD9';
    pluralName: 'enum-sources';
    singularName: 'enum-source';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    host: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::enum-source.enum-source'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    name_en: Schema.Attribute.String;
    name_short: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEonlyEonly extends Struct.CollectionTypeSchema {
  collectionName: 'eonlies';
  info: {
    description: '';
    displayName: '\u591A\u4E1A\u52A1\u5E93/E-ONLY\u8D44\u6E90';
    pluralName: 'eonlies';
    singularName: 'eonly';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    abstract: Schema.Attribute.Text;
    author: Schema.Attribute.Text;
    author_org: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    detailurl: Schema.Attribute.Text;
    doi: Schema.Attribute.String;
    eissn: Schema.Attribute.String;
    end_page: Schema.Attribute.String;
    files: Schema.Attribute.Text;
    files_normal: Schema.Attribute.Text;
    identifier: Schema.Attribute.String;
    issn: Schema.Attribute.String;
    issue: Schema.Attribute.String;
    keyword: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::eonly.eonly'> &
      Schema.Attribute.Private;
    order_id: Schema.Attribute.String;
    page_num: Schema.Attribute.String;
    pdf_file_path: Schema.Attribute.Text;
    pub_year: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    publisher: Schema.Attribute.String;
    source_title: Schema.Attribute.String;
    start_page: Schema.Attribute.String;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
    unique_md5: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    volume: Schema.Attribute.String;
  };
}

export interface ApiExcelExcel extends Struct.CollectionTypeSchema {
  collectionName: 'excels';
  info: {
    description: '';
    displayName: '\u6570\u636E\u5BFC\u5165\u8BB0\u5F55';
    pluralName: 'excels';
    singularName: 'excel';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    create_count: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    file: Schema.Attribute.Media<'files'> & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::excel.excel'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.String & Schema.Attribute.Required;
    update_count: Schema.Attribute.Integer;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiJournalJournal extends Struct.CollectionTypeSchema {
  collectionName: 'journals';
  info: {
    displayName: '\u591A\u4E1A\u52A1\u5E93/OA\u671F\u520A';
    pluralName: 'journals';
    singularName: 'journal';
  };
  options: {
    comment: '';
    draftAndPublish: false;
  };
  attributes: {
    abstract: Schema.Attribute.Text;
    article_trans_abstract: Schema.Attribute.Text;
    article_trans_subtitle: Schema.Attribute.Text;
    article_trans_title: Schema.Attribute.Text;
    article_type: Schema.Attribute.String;
    auth_email: Schema.Attribute.Text;
    author: Schema.Attribute.Text;
    author_org: Schema.Attribute.Text;
    city: Schema.Attribute.Text;
    classification: Schema.Attribute.String;
    country: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    database_id: Schema.Attribute.String;
    detailurl: Schema.Attribute.Text;
    doi: Schema.Attribute.String;
    end_page: Schema.Attribute.String;
    file_path_url: Schema.Attribute.Text;
    files: Schema.Attribute.String;
    files_normal: Schema.Attribute.Text;
    full_name: Schema.Attribute.String;
    identifier: Schema.Attribute.String;
    institution: Schema.Attribute.String;
    institution_id: Schema.Attribute.String;
    issue: Schema.Attribute.String;
    issue_part: Schema.Attribute.String;
    issue_total: Schema.Attribute.String;
    keyword: Schema.Attribute.Text;
    keyword_language: Schema.Attribute.String;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::journal.journal'
    > &
      Schema.Attribute.Private;
    oa_id: Schema.Attribute.String;
    page_info: Schema.Attribute.String;
    page_num: Schema.Attribute.String;
    postal_code: Schema.Attribute.Text;
    process_date: Schema.Attribute.String;
    process_level: Schema.Attribute.String;
    process_mode: Schema.Attribute.String;
    pub_year: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    source_id: Schema.Attribute.String;
    source_issn: Schema.Attribute.Text;
    source_title: Schema.Attribute.Text;
    start_page: Schema.Attribute.String;
    state: Schema.Attribute.Text;
    subtitle: Schema.Attribute.Text;
    supplement: Schema.Attribute.String;
    title: Schema.Attribute.Text;
    unique_md5: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    volume: Schema.Attribute.String;
    volume_id: Schema.Attribute.String;
  };
}

export interface ApiLiteratureLiterature extends Struct.CollectionTypeSchema {
  collectionName: 'literatures';
  info: {
    description: '';
    displayName: '\u6D77\u5916\u6587\u732E\u5E93/\u5143\u6570\u636E';
    pluralName: 'literatures';
    singularName: 'literature';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    abstract: Schema.Attribute.Text;
    access_type: Schema.Attribute.String & Schema.Attribute.Required;
    access_type_normal: Schema.Attribute.String;
    author: Schema.Attribute.Text;
    author_org: Schema.Attribute.Text;
    caas_discipline: Schema.Attribute.String;
    caas_discipline_normal: Schema.Attribute.String;
    category: Schema.Attribute.String & Schema.Attribute.Required;
    category_normal: Schema.Attribute.String;
    collection_id: Schema.Attribute.Text;
    collection_name: Schema.Attribute.Text;
    collection_time: Schema.Attribute.String;
    collection_url: Schema.Attribute.Text;
    content_type_es: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<'literature'>;
    country: Schema.Attribute.String;
    country_normal: Schema.Attribute.String;
    cover: Schema.Attribute.Text;
    cover_normal: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    detail_url: Schema.Attribute.Text & Schema.Attribute.Required;
    doi: Schema.Attribute.String;
    files: Schema.Attribute.Text;
    files_normal: Schema.Attribute.Text;
    identifier: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    images: Schema.Attribute.Text;
    images_normal: Schema.Attribute.Text;
    industry: Schema.Attribute.String;
    industry_normal: Schema.Attribute.String;
    isbn: Schema.Attribute.String;
    issn: Schema.Attribute.String;
    keyword: Schema.Attribute.Text;
    language: Schema.Attribute.String & Schema.Attribute.Required;
    language_normal: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::literature.literature'
    > &
      Schema.Attribute.Private;
    normed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    other_data: Schema.Attribute.Text;
    page_info: Schema.Attribute.String;
    pub_date: Schema.Attribute.String;
    pub_year: Schema.Attribute.String;
    publish_cycle: Schema.Attribute.String;
    publish_cycle_normal: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    remark: Schema.Attribute.Text;
    risk_rating: Schema.Attribute.String & Schema.Attribute.Required;
    risk_rating_normal: Schema.Attribute.String;
    source: Schema.Attribute.Text & Schema.Attribute.Required;
    source_normal: Schema.Attribute.Text;
    subtitle: Schema.Attribute.Text;
    support_project: Schema.Attribute.Text;
    title: Schema.Attribute.Text & Schema.Attribute.Required;
    topic: Schema.Attribute.String;
    unique_md5: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    use_type: Schema.Attribute.String & Schema.Attribute.Required;
    use_type_normal: Schema.Attribute.String;
  };
}

export interface ApiPaperPaper extends Struct.CollectionTypeSchema {
  collectionName: 'papers';
  info: {
    description: '';
    displayName: '\u5B66\u4F4D\u8BBA\u6587\u5E93/\u5143\u6570\u636E';
    pluralName: 'papers';
    singularName: 'paper';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    application_degree_type: Schema.Attribute.String &
      Schema.Attribute.Required;
    author: Schema.Attribute.String & Schema.Attribute.Required;
    awarding_body: Schema.Attribute.String & Schema.Attribute.Required;
    call_number: Schema.Attribute.String;
    call_string: Schema.Attribute.String;
    call_string_normal: Schema.Attribute.String;
    clc: Schema.Attribute.String;
    collection_institution: Schema.Attribute.String & Schema.Attribute.Required;
    collection_string: Schema.Attribute.String;
    control_number: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    degree_conferring_time: Schema.Attribute.String & Schema.Attribute.Required;
    degree_type: Schema.Attribute.String & Schema.Attribute.Required;
    degree_type_normal: Schema.Attribute.String;
    discipline: Schema.Attribute.String;
    discipline_normal: Schema.Attribute.String;
    download_count: Schema.Attribute.Integer;
    identifier: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    is_good_paper: Schema.Attribute.String;
    keywords: Schema.Attribute.Text & Schema.Attribute.Required;
    language_of_work: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::paper.paper'> &
      Schema.Attribute.Private;
    normed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    page: Schema.Attribute.String & Schema.Attribute.Required;
    pdf_abstract: Schema.Attribute.Text;
    pdf_abstract_en: Schema.Attribute.Text;
    pdf_all_file: Schema.Attribute.String;
    pdf_all_file_normal: Schema.Attribute.Text;
    pdf_pre_file: Schema.Attribute.String;
    pdf_pre_file_normal: Schema.Attribute.Text;
    professional_field_entry: Schema.Attribute.String &
      Schema.Attribute.Required;
    professional_field_navigation: Schema.Attribute.String &
      Schema.Attribute.Required;
    professional_field_navigation_normal: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    remarks: Schema.Attribute.String;
    research_direction: Schema.Attribute.String & Schema.Attribute.Required;
    title: Schema.Attribute.Text & Schema.Attribute.Required;
    title_en: Schema.Attribute.Text & Schema.Attribute.Required;
    training_unit: Schema.Attribute.String;
    training_unit_normal: Schema.Attribute.String;
    unique_md5: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    view_count: Schema.Attribute.Integer;
  };
}

export interface PluginContentReleasesRelease
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_releases';
  info: {
    displayName: 'Release';
    pluralName: 'releases';
    singularName: 'release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    actions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    releasedAt: Schema.Attribute.DateTime;
    scheduledAt: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Schema.Attribute.Required;
    timezone: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_release_actions';
  info: {
    displayName: 'Release Action';
    pluralName: 'release-actions';
    singularName: 'release-action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentType: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    entryDocumentId: Schema.Attribute.String;
    isEntryValid: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    release: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::content-releases.release'
    >;
    type: Schema.Attribute.Enumeration<['publish', 'unpublish']> &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginElasticsearchIndexingLog
  extends Struct.CollectionTypeSchema {
  collectionName: 'indexing-log';
  info: {
    description: 'Logged runs of the indexing cron job';
    displayName: 'Indexing Logs';
    pluralName: 'indexing-logs';
    singularName: 'indexing-log';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    details: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::elasticsearch.indexing-log'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<['pass', 'fail']> &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginElasticsearchTask extends Struct.CollectionTypeSchema {
  collectionName: 'task';
  info: {
    description: 'Search indexing tasks';
    displayName: 'Task';
    pluralName: 'tasks';
    singularName: 'task';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    collection_name: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    full_site_indexing: Schema.Attribute.Boolean;
    indexing_status: Schema.Attribute.Enumeration<['to-be-done', 'done']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'to-be-done'>;
    indexing_type: Schema.Attribute.Enumeration<
      ['add-to-index', 'remove-from-index']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'add-to-index'>;
    item_document_id: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::elasticsearch.task'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
  collectionName: 'i18n_locale';
  info: {
    collectionName: 'locales';
    description: '';
    displayName: 'Locale';
    pluralName: 'locales';
    singularName: 'locale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::i18n.locale'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.SetMinMax<
        {
          max: 50;
          min: 1;
        },
        number
      >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflow
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows';
  info: {
    description: '';
    displayName: 'Workflow';
    name: 'Workflow';
    pluralName: 'workflows';
    singularName: 'workflow';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentTypes: Schema.Attribute.JSON &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'[]'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    stageRequiredToPublish: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::review-workflows.workflow-stage'
    >;
    stages: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflowStage
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows_stages';
  info: {
    description: '';
    displayName: 'Stages';
    name: 'Workflow Stage';
    pluralName: 'workflow-stages';
    singularName: 'workflow-stage';
  };
  options: {
    draftAndPublish: false;
    version: '1.1.0';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    color: Schema.Attribute.String & Schema.Attribute.DefaultTo<'#4945FF'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    permissions: Schema.Attribute.Relation<'manyToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    workflow: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::review-workflows.workflow'
    >;
  };
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
  collectionName: 'files';
  info: {
    description: '';
    displayName: 'File';
    pluralName: 'files';
    singularName: 'file';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    alternativeText: Schema.Attribute.String;
    caption: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ext: Schema.Attribute.String;
    folder: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'> &
      Schema.Attribute.Private;
    folderPath: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    formats: Schema.Attribute.JSON;
    hash: Schema.Attribute.String & Schema.Attribute.Required;
    height: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.file'
    > &
      Schema.Attribute.Private;
    mime: Schema.Attribute.String & Schema.Attribute.Required;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    previewUrl: Schema.Attribute.String;
    provider: Schema.Attribute.String & Schema.Attribute.Required;
    provider_metadata: Schema.Attribute.JSON;
    publishedAt: Schema.Attribute.DateTime;
    related: Schema.Attribute.Relation<'morphToMany'>;
    size: Schema.Attribute.Decimal & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String & Schema.Attribute.Required;
    width: Schema.Attribute.Integer;
  };
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
  collectionName: 'upload_folders';
  info: {
    displayName: 'Folder';
    pluralName: 'folders';
    singularName: 'folder';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    children: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.folder'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    files: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.file'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.folder'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    parent: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'>;
    path: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    pathId: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.String & Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    >;
  };
}

export interface PluginUsersPermissionsUser
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'user';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
    confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    > &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ContentTypeSchemas {
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::permission': AdminPermission;
      'admin::role': AdminRole;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'admin::user': AdminUser;
      'api::book.book': ApiBookBook;
      'api::bulletin.bulletin': ApiBulletinBulletin;
      'api::conference.conference': ApiConferenceConference;
      'api::enum-caas.enum-caas': ApiEnumCaasEnumCaas;
      'api::enum-country.enum-country': ApiEnumCountryEnumCountry;
      'api::enum-literature-access-type.enum-literature-access-type': ApiEnumLiteratureAccessTypeEnumLiteratureAccessType;
      'api::enum-literature-category.enum-literature-category': ApiEnumLiteratureCategoryEnumLiteratureCategory;
      'api::enum-literature-industry.enum-literature-industry': ApiEnumLiteratureIndustryEnumLiteratureIndustry;
      'api::enum-literature-language.enum-literature-language': ApiEnumLiteratureLanguageEnumLiteratureLanguage;
      'api::enum-literature-publish-cycle.enum-literature-publish-cycle': ApiEnumLiteraturePublishCycleEnumLiteraturePublishCycle;
      'api::enum-literature-risk-rating.enum-literature-risk-rating': ApiEnumLiteratureRiskRatingEnumLiteratureRiskRating;
      'api::enum-literature-use-type.enum-literature-use-type': ApiEnumLiteratureUseTypeEnumLiteratureUseType;
      'api::enum-paper-call-string.enum-paper-call-string': ApiEnumPaperCallStringEnumPaperCallString;
      'api::enum-paper-degree-type.enum-paper-degree-type': ApiEnumPaperDegreeTypeEnumPaperDegreeType;
      'api::enum-paper-discipline.enum-paper-discipline': ApiEnumPaperDisciplineEnumPaperDiscipline;
      'api::enum-paper-professional-field-navigation.enum-paper-professional-field-navigation': ApiEnumPaperProfessionalFieldNavigationEnumPaperProfessionalFieldNavigation;
      'api::enum-paper-training-unit.enum-paper-training-unit': ApiEnumPaperTrainingUnitEnumPaperTrainingUnit;
      'api::enum-source.enum-source': ApiEnumSourceEnumSource;
      'api::eonly.eonly': ApiEonlyEonly;
      'api::excel.excel': ApiExcelExcel;
      'api::journal.journal': ApiJournalJournal;
      'api::literature.literature': ApiLiteratureLiterature;
      'api::paper.paper': ApiPaperPaper;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::elasticsearch.indexing-log': PluginElasticsearchIndexingLog;
      'plugin::elasticsearch.task': PluginElasticsearchTask;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::review-workflows.workflow': PluginReviewWorkflowsWorkflow;
      'plugin::review-workflows.workflow-stage': PluginReviewWorkflowsWorkflowStage;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
    }
  }
}
