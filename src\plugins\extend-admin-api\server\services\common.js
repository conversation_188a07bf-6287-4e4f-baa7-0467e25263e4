'use strict'
const crypto = require('node:crypto')
const { errors } = require('@strapi/utils')
const bookSchema = require('../../../../api/book/content-types/book/schema.json')
const bulletinSchema = require('../../../../api/bulletin/content-types/bulletin/schema.json')
const conferenceSchema = require('../../../../api/conference/content-types/conference/schema.json')
const enumLiteratureCaasDisciplineSchema = require('../../../../api/enum-caas/content-types/enum-caas/schema.json')
const enumLiteratureCountrySchema = require('../../../../api/enum-country/content-types/enum-country/schema.json')
const enumLiteratureAccessTypeSchema = require('../../../../api/enum-literature-access-type/content-types/enum-literature-access-type/schema.json')
const enumLiteratureCategorySchema = require('../../../../api/enum-literature-category/content-types/enum-literature-category/schema.json')
const enumLiteratureIndustrySchema = require('../../../../api/enum-literature-industry/content-types/enum-literature-industry/schema.json')
const enumLiteratureLanguageSchema = require('../../../../api/enum-literature-language/content-types/enum-literature-language/schema.json')
const enumLiteraturePublishCycleSchema = require('../../../../api/enum-literature-publish-cycle/content-types/enum-literature-publish-cycle/schema.json')
const enumLiteratureRiskRatingSchema = require('../../../../api/enum-literature-risk-rating/content-types/enum-literature-risk-rating/schema.json')
const enumLiteratureUseTypeSchema = require('../../../../api/enum-literature-use-type/content-types/enum-literature-use-type/schema.json')
const enumPaperCallStringSchema = require('../../../../api/enum-paper-call-string/content-types/enum-paper-call-string/schema.json')
const enumPaperDegreeTypeSchema = require('../../../../api/enum-paper-degree-type/content-types/enum-paper-degree-type/schema.json')
const enumPaperDisciplineSchema = require('../../../../api/enum-paper-discipline/content-types/enum-paper-discipline/schema.json')
const enumPaperProfessionalFieldNavigationSchema = require('../../../../api/enum-paper-professional-field-navigation/content-types/enum-paper-professional-field-navigation/schema.json')
const enumPaperTrainingUnitSchema = require('../../../../api/enum-paper-training-unit/content-types/enum-paper-training-unit/schema.json')
const enumLiteratureSourceSchema = require('../../../../api/enum-source/content-types/enum-source/schema.json')
const eonlySchema = require('../../../../api/eonly/content-types/eonly/schema.json')
const journalSchema = require('../../../../api/journal/content-types/journal/schema.json')
const literatureSchema = require('../../../../api/literature/content-types/literature/schema.json')
const paperSchema = require('../../../../api/paper/content-types/paper/schema.json')
const UtilOSS = require('../../../../utils/oss')

const { ApplicationError } = errors
/**
 * common service
 */
module.exports = ({ strapi }) => ({

  /**
   * 生成md5值
   * @param data
   * @returns {string}
   */
  md5(data) {
    return crypto.createHash('md5').update(data).digest('hex')
  },
  /**
   * 标准化数据
   * 不包括文件类
   * @param data
   * @param type
   * @param userID
   * @returns {Promise<{formatData: *[], errorMessage: *[]}>}
   */
  async formatData({ data, type, userID, action }) {
    const errorMessage = []
    const connectItems = []
    let index = 0
    const associateData = async () => {
      if (data.length > 0) {
        const item = data.shift()
        index++
        const errorObj = {}
        // 针对每一行每一个字段进行处理
        await Promise.all(Object.keys(item).map(async (key) => {
          // 处理normal相关数据，如数据为空则需要清空数据值
          let extRelatedTable = null
          if (this.getContentTypes(type) && this.getContentTypes(type).attributes && this.getContentTypes(type).attributes[key] && this.getContentTypes(type).attributes[key].extIsNormal) {
            extRelatedTable = this.getContentTypes(type).attributes[key].extRelatedTable
          }

          if (extRelatedTable) {
            if (!item[key]) {
              // extRelatedTable.associatedFieldName 关联normal的字段名称
              item[extRelatedTable.associatedFieldName] = null
              return null
            }

            // 去重数据
            const normalParams = Array.from(new Set(item[key].split(extRelatedTable.separator))).map(v => v.trim()).filter(v => v)
            let normalResult = null
            normalResult = await strapi.documents(`api::${extRelatedTable.tableName}.${extRelatedTable.tableName}`).findMany({
              filters: {
                // extRelatedTable.validateField->normal表中需要关联表中校验的字段（name等）
                $or: extRelatedTable.validateField.map(v => ({
                  [v]: {
                    $in: normalParams,
                  },
                })),
              },
            })

            // 关联数据
            if (normalResult.length === 0) {
              // item[extRelatedTable.associatedFieldName] = {
              //   set: [],
              // }

              item[extRelatedTable.associatedFieldName] = null
            }
            else {
              // item[extRelatedTable.associatedFieldName] = {
              //   set: normalResult.map(v => v.id),
              // }

              item[extRelatedTable.associatedFieldName] = normalResult.map(v => v.name).join(extRelatedTable.endingCharacter)
            }
            // 数据内容不匹配，记录错误日志
            if (normalResult.length !== normalParams.length) {
              errorObj[key] = {
                message: '数据内容不匹配',
                value: normalParams.filter(
                  v => !normalResult.find(res => res === v),
                ),
              }
            }
          }

          return null
        }))
        // 初始化normed的数据为false
        let normed = false
        // 当错误信息的Object的key的长度大于0时，需要讲行号和ID以及错误信息push到最外层的数组中
        if (Object.keys(errorObj).length > 0) {
          errorObj.index = index
          errorObj.id = item.id
          errorMessage.push(errorObj)
        }
        else {
          // 如果没有错误信息，需要将normed的状态修改为true（true代表可以发布。当此字段为true时，批量格式化时不处理）
          // 同时这里保持publishedAt的状态，不对源数据的publishedAt进行更改
          normed = true
        }
        const documentId = item.documentId
        // 标准化只针对关联后的normal字段进行处理，对于其他字段以及normal对应的原始字段不进行处理，并且做到删除防止进行错误修改。
        // 比如api上传的files（数组）和网页提供的files（字符串）不一致导致错误修改
        Object.keys(item).forEach((key) => {
          if (!item[key] /* || (item[key].connect === undefined && item[key].set === undefined) */) {
            delete item[key]
          }
        })
        // format时不执行lifecycles
        item.updateType = 'format'
        item.normed = normed
        if (action !== 'published') {
          await strapi.documents(`api::${type}.${type}`).update({
            documentId,
            data: {
              ...item,
              updatedBy: userID,
            },
          })
        }
        connectItems.push(item)

        await associateData()
      }
    }
    await associateData()
    return { formatData: connectItems, errorMessage }
  },
  /**
   * 获取表字段数据
   * @param type
   * @returns {*}
   */
  getContentTypes(type) {
    const schemas = [
      literatureSchema,
      bookSchema,
      bulletinSchema,
      conferenceSchema,
      eonlySchema,
      journalSchema,
      enumLiteratureAccessTypeSchema,
      enumLiteratureCaasDisciplineSchema,
      enumLiteratureCategorySchema,
      enumLiteratureCountrySchema,
      enumLiteratureIndustrySchema,
      enumLiteratureLanguageSchema,
      enumLiteraturePublishCycleSchema,
      enumLiteratureRiskRatingSchema,
      enumLiteratureSourceSchema,
      enumLiteratureUseTypeSchema,
      paperSchema,
      enumPaperCallStringSchema,
      enumPaperDegreeTypeSchema,
      enumPaperDisciplineSchema,
      enumPaperProfessionalFieldNavigationSchema,
      enumPaperTrainingUnitSchema,
    ]

    const rules = schemas.reduce((acc, schema) => {
      const key = schema.info.singularName
      acc[key] = schema
      return acc
    }, {})
    return rules[type]
  },
  /**
   * 生命周期函数处理内容
   * 1. 移动文件（新增添加文件、编辑移动文件、删除清理文件）
   * 2. 检查 MD5 的内容重复
   * 3. 发布检查，是否全部标准化
   * # 备注
   * 只有文档API findMany触发many相关函数（before(after) findMany），其余不触发。
   * 发布&草稿数据库中会存在两条数据
   * 当草稿修改并不保存时，发布的数据为上一次的草稿数据
   * lifecycles数据创建之前的操作
   * @param event
   * @returns {Promise<void>}
   */
  /**
   * 创建数据之前验证逻辑
   * @param event
   * @returns {Promise<void>}
   */
  async beforeCreate(event) {
    const utilOSSInstance = new UtilOSS()
    const { data } = event.params
    const { uid, attributes, singularName } = event.model

    // 草稿转换成发布数据时，会进行旧发布数据的删除和新发布数据的创建
    // 发布时进行标准化数据的验证、关联（需要查询schema是否需要标准化）
    const contentType = this.getContentTypes(singularName)
    if (data.publishedAt && data.documentId && contentType && contentType.extIsStandardizedData) {
      const documentResult = await strapi.documents(uid).findOne({
        documentId: data.documentId,
      })

      // 这里的标准化会导致草稿数据的修改，这里只验证标准化而不去修改。标准化在修改草稿的时候进行。
      const formatResult = await this.formatData({ data: [documentResult], type: singularName, action: 'published' })

      if (formatResult.errorMessage.length > 0) {
        throw new ApplicationError('发布失败，请规范化数据后重新尝试发布', { errorMessage: formatResult.errorMessage })
      }
    }
    // 如果有extMd5Field字段则验证md5值
    if (contentType && contentType.extMd5Field && contentType.extMd5Field.length !== 0) {
      // 1.生成内容md5，检查数据是否重复，重复不允许录入。
      const str = contentType.extMd5Field.map(v => data[v] ? data[v] : '').join('')
      const contentMD5 = this.md5(str)

      const documentResult = await strapi.documents(uid).findMany({
        filters: {
          unique_md5: contentMD5,
        },
      })
      if (documentResult && documentResult.length > 0) {
        // throw new ApplicationError('标题内容重复，请检查', { data })
        if (!data.publishedAt) {
          throw new ApplicationError(`标题内容重复，请检查 ${data.identifier}`, { data })
        }
      }
      data.unique_md5 = contentMD5
    }
    // 创建前判断必须有唯一标识
    const extUniqueField = contentType.extUniqueField[0]
    if (!data[extUniqueField]) {
      throw new ApplicationError('Excel中缺少唯一标识信息，请补充填写。', { data })
    }

    // 将文件从upload_temp移动到指定目录
    // 文件不存在则拒绝录入，除了paper
    if (!data.files && !['paper'].includes(singularName)) {
      throw new ApplicationError('Excel中缺少文件信息，请补充填写。', { data })
    }

    if (['paper'].includes(singularName) && (!data.pdf_pre_file || !data.pdf_all_file)) {
      throw new ApplicationError('Excel中缺少文件信息，请补充填写。', { data })
    }

    // 移动文件
    if (data.files && !['paper'].includes(singularName)) {
      const filesExtRelatedTable = attributes.files.extRelatedTable
      if (data.dataSource === 'api') {
        data.files = JSON.parse(data.files)
      }
      else {
        // 由于用户无法在管理面板修改文件相关字段，取消验证dataSource为excel改为默认为excel。
        data.files = data.files.split(filesExtRelatedTable.separator).map(v => ({ name: v.trim() }))
      }
      const uploadFileResult = await utilOSSInstance.moveFiles({
        target: data.identifier,
        files: data.files,
        type: singularName,
        splitCharacters: filesExtRelatedTable.splitCharacters,
      })
      data.files_normal = uploadFileResult.join(filesExtRelatedTable.endingCharacter)

      data.files = data.files.map(v => v.name).join(filesExtRelatedTable.separator)
    }

    // 移动封面

    if (data.cover) {
      const coverExtRelatedTable = attributes.cover.extRelatedTable
      // 封面为单文件
      // 由于用户无法在管理面板修改文件相关字段，取消验证dataSource为excel改为默认为excel。
      // if (data.dataSource === 'excel') {
      //   data.cover = { name: data.cover }
      // }
      // else
      if (data.dataSource === 'api') {
        data.cover = JSON.parse(data.cover)
      }
      else {
        data.cover = { name: data.cover }
      }
      if (typeof data.cover === 'object') {
        const uploadFileResult = await utilOSSInstance.moveFiles({
          target: data.identifier,
          files: [data.cover],
          type: singularName,
          splitCharacters: coverExtRelatedTable.splitCharacters,
        })
        data.cover_normal = uploadFileResult.join(coverExtRelatedTable.endingCharacter)
        data.cover = data.cover.name
      }
    }

    // 移动图片

    if (data.images) {
      const imagesExtRelatedTable = attributes.images.extRelatedTable
      // 图片为多文件
      // 由于用户无法在管理面板修改文件相关字段，取消验证dataSource为excel改为默认为excel。
      // if (data.dataSource === 'excel') {
      //   data.images = data.images.split('||').map(v => ({ name: v.trim() }))
      // }
      // else
      if (data.dataSource === 'api') {
        data.images = JSON.parse(data.images)
      }
      else {
        data.images = data.images.split(imagesExtRelatedTable.separator).map(v => ({ name: v.trim() }))
      }
      if (data.images.length > 0) {
        const uploadFileResult = await utilOSSInstance.moveFiles({
          target: data.identifier,
          files: data.images,
          type: singularName,
          splitCharacters: imagesExtRelatedTable.splitCharacters,
        })
        data.images_normal = uploadFileResult.join(imagesExtRelatedTable.endingCharacter)

        data.images = data.images.map(v => v.name).join(imagesExtRelatedTable.separator)
      }
    }
    // 移动paper pdf全文
    if (data.pdf_all_file) {
      const pdfAllFileExtRelatedTable = attributes.pdf_all_file.extRelatedTable
      if (data.dataSource === 'api') {
        data.pdf_all_file = JSON.parse(data.pdf_all_file)
      }
      else {
        data.pdf_all_file = data.pdf_all_file.split(pdfAllFileExtRelatedTable.separator).map(v => ({ name: v.trim() }))
      }

      if (data.pdf_all_file.length > 0) {
        const uploadFileNameResult = await utilOSSInstance.moveFiles({
          target: data.identifier,
          files: data.pdf_all_file,
          type: singularName,
          splitCharacters: pdfAllFileExtRelatedTable.splitCharacters,
        })
        data.pdf_all_file_normal = uploadFileNameResult.join(pdfAllFileExtRelatedTable.endingCharacter)

        data.pdf_all_file = data.pdf_all_file.map(v => v.name).join(pdfAllFileExtRelatedTable.separator)
      }
    }
    // 处理paper 18页 pdf
    if (data.pdf_pre_file) {
      const pdfPreFileExtRelatedTable = attributes.pdf_pre_file.extRelatedTable
      if (data.dataSource === 'api') {
        data.pdf_pre_file = JSON.parse(data.pdf_pre_file)
      }
      else {
        data.pdf_pre_file = data.pdf_pre_file.split(pdfPreFileExtRelatedTable.separator).map(v => ({ name: v.trim() }))
      }
      if (data.pdf_pre_file.length > 0) {
        const uploadFileNameResult = await utilOSSInstance.moveFiles({
          target: data.identifier,
          files: data.pdf_pre_file,
          type: singularName,
          splitCharacters: pdfPreFileExtRelatedTable.splitCharacters,
        })
        data.pdf_pre_file_normal = uploadFileNameResult.join(pdfPreFileExtRelatedTable.endingCharacter)

        data.pdf_pre_file = data.pdf_pre_file.map(v => v.name).join(pdfPreFileExtRelatedTable.separator)
      }
    }
  },
  /**
   * 数据修改之前的验证
   * 1.数据发布，检查数据是否标准化
   * 2.数据修改，根据内容生成md5并检查数据是否重复，重复拒绝修改。
   * 3.根据修改文件类型的字段，进行操作文件。
   * 4.修改类型为format时，只会修改normal相关字段，format类型时不检查其他字段是否更改，包括文件。
   // * 5.当where中年存在ID时并且updateType为undefined代表后台修改数据，不进行文件的校验。
   * @param event
   * @returns {Promise<void>}
   */
  async beforeUpdate(event) {
    const { data, where } = event.params
    const { uid, attributes, singularName } = event.model

    // 数据修改类型为format时，不处理其他数据（normal以外的数据）
    if (data.updateType === 'format') {
      // 标准化时由于后台无法修改文件不进行标准化数据处理，只有当api修改时标准化数据
      return
    }
    const utilOSSInstance = new UtilOSS()

    // 修改时判断md5是否重复
    // 由于format时只会关联normal不存在修改其他数据的情况，format时不校验md5值
    const contentType = this.getContentTypes(singularName)
    if (contentType && contentType.extMd5Field && contentType.extMd5Field.length !== 0) {
      const str = contentType.extMd5Field.map(v => data[v] ? data[v] : '').join('')
      const contentMD5 = this.md5(str)

      const documentResult = await strapi.documents(uid).findMany({
        filters: {
          unique_md5: contentMD5,
        },
      })
      if (documentResult && documentResult.filter(v => v.id !== where.id).length > 0) {
        throw new ApplicationError('标题内容重复，请检查', { data: { contentMD5 } })
      }
      data.unique_md5 = contentMD5
    }
    // 修改前判断必须有唯一标识
    const extUniqueField = contentType.extUniqueField[0]
    if (!data[extUniqueField]) {
      throw new ApplicationError('Excel中缺少唯一标识信息，请补充填写。', { data })
    }
    if (data.files) {
      const filesExtRelatedTable = attributes.files.extRelatedTable

      if (data.dataSource === 'api') {
        data.files = JSON.parse(data.files)
      }
      else {
        // 默认files的名称从excel导入
        data.files = data.files.split(filesExtRelatedTable.separator).map(v => ({ name: v.trim() }))
      }
      if (data.files.length > 0) {
        const uploadFileResult = await utilOSSInstance.moveFiles({
          target: data.identifier,
          files: data.files,
          type: singularName,
          splitCharacters: filesExtRelatedTable.splitCharacters,
        })
        data.files_normal = uploadFileResult.join(filesExtRelatedTable.endingCharacter)
        data.files = data.files.map(v => v.name).join(filesExtRelatedTable.separator)
      }
    }
    else {
      // 标准化时由于后台无法修改文件不进行标准化数据处理，只有当api修改时标准化数据
      data.files = null
      data.files_normal = null
    }

    // 移动封面
    if (data.cover) {
      const coverExtRelatedTable = attributes.cover.extRelatedTable
      if (data.dataSource === 'api') {
        data.cover = JSON.parse(data.cover)
      }
      else {
        data.cover = { name: data.cover }
      }
      if (typeof data.cover === 'object') {
        const uploadFileCoverResult = await utilOSSInstance.moveFiles({
          target: data.identifier,
          files: [data.cover],
          type: singularName,
          splitCharacters: coverExtRelatedTable.splitCharacters,
        })
        data.cover_normal = uploadFileCoverResult.join(coverExtRelatedTable.endingCharacter)
        data.cover = data.cover.name
      }
    }
    else {
      data.cover = null
      data.cover_normal = null
    }

    // 移动
    if (data.images) {
      const imagesExtRelatedTable = attributes.images.extRelatedTable
      if (data.dataSource === 'api') {
        data.images = JSON.parse(data.images)
      }
      else {
        data.images = data.images.split(imagesExtRelatedTable.separator).map(v => ({ name: v.trim() }))
      }
      if (data.images.length > 0) {
        const uploadFileResult = await utilOSSInstance.moveFiles({
          target: data.identifier,
          files: data.images,
          type: singularName,
          splitCharacters: imagesExtRelatedTable.splitCharacters,
        })
        data.images_normal = uploadFileResult.join(imagesExtRelatedTable.endingCharacter)

        data.images = data.images.map(v => v.name).join(imagesExtRelatedTable.separator)
      }
    }
    else {
      data.images = null
      data.images_normal = null
    }
  },
  /**
   * 修改草稿数据后，进行标准化操作
   * @param event
   * @returns {Promise<void>}
   */
  async afterUpdate(event) {
    const { data, where } = event.params
    const { uid, singularName } = event.model
    const contentType = this.getContentTypes(singularName)
    if (data && data.updateType !== 'format' && contentType.extIsStandardizedData) {
      const documentResult = await strapi.documents(uid).findFirst({
        filters: {
          id: where.id,
        },
      })
      await this.formatData({ data: [documentResult], type: singularName })
    }
  },
  /**
   * 1.删除数据后，根据唯一标识（identifier）删除对应文件夹下的所有内容
   * 2.批量删除时，使用的是文档API delete方法，仅触发before(after) Delete
   * 3.使用1.Discard changes、2.删除数据（如果是发布状态会触发第三步）、3.取消发布（删除发布的数据，会带有publishedAt字段）都会触发此方法
   * 4.如果对数据进行Discard changes会将当前的草稿删除（将发布的数据覆盖到草稿），并新建一个新的草稿（如果这里删除文件会导致文件异常删除），只有发布才会有Discard changes
   * @param event
   * @returns {Promise<void>}
   */
  async afterDelete(event) {
    // 数据修改（二次发布、取消发布）发布状态（非首次修改）时会触发删除函数，将以前发布的内容删除。不会删除草稿数据。
    const { result } = event

    const { uid, singularName } = event.model
    const contentType = this.getContentTypes(singularName)
    // 不删除草稿数据时，无需删除文件
    // 草稿数据的ID不变，每一次发布后，发布数据的ID都会改变
    // result.publishedAt===null 操作草稿数据
    if (result.publishedAt === null && contentType && contentType.attributes && contentType.attributes.files) {
      // 需要查询数据是否为真正的删除
      const utilOSSInstance = new UtilOSS()
      const documentPublished = await strapi.documents(uid).findOne({
        documentId: result.documentId,
        status: 'published',
      })
      // 如果查询发布的数据存在，则判断为Discard changes操作，无需删除文件
      if (!documentPublished) {
        await utilOSSInstance.deleteFolder(result.identifier, singularName)
      }
    }
  },
})
