{"name": "facpisp-literature-api", "version": "0.1.0", "private": true, "description": "A Strapi application", "author": {"name": "A Strapi developer"}, "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=6.0.0"}, "scripts": {"develop": "cross-env ENV_PATH=.env.development strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@geeky-biz/strapi-plugin-elasticsearch": "^0.0.10", "@strapi/plugin-cloud": "5.11.3", "@strapi/plugin-users-permissions": "5.11.3", "@strapi/strapi": "5.11.3", "ali-oss": "^6.22.0", "antd": "^5.24.5", "cross-env": "^7.0.3", "exceljs": "^4.4.0", "ip-num": "^1.5.2", "mysql2": "^3.14.0", "pg": "^8.14.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.1.16", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@eslint-react/eslint-plugin": "^1.42.1", "eslint": "^9.23.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19"}, "strapi": {"uuid": "36fad857-f8db-4f7d-958f-d4d33c37fd5c"}}