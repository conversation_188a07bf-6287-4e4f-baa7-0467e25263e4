'use strict'

const { errors } = require('@strapi/utils')
const { createCoreService } = require('@strapi/strapi').factories

/**
 * literature service
 * 1. 提供生命周期使用的方法
 * 2. 自身的方法（标准化）
 */
module.exports = createCoreService('api::literature.literature', ({ strapi }) => ({

  /**
   * api 创建文献
   * @param data
   * @param userID
   * @returns {Promise<{}>}
   */
  async apiCreate(data, userID) {
    ['cover', 'images', 'files'].forEach((key) => {
      if (data[key] && typeof data[key] === 'object') {
        data.dataSource = 'api'
        if (Array.isArray(data[key]) && data[key].length === 0) {
          delete data[key]
        }
        else {
          data[key] = JSON.stringify(data[key])
        }
      }
    })
    // 通过唯一标识identifier，查询数据。
    const result = await strapi.db.query('api::literature.literature').findOne({
      select: ['id', 'documentId'],
      where: { identifier: data.identifier },
    })
    const responseResult = {}
    let documentId = null
    if (result) {
      // 查询到数据，进行修改
      responseResult.updateResult = await strapi.documents(`api::literature.literature`).update({
        documentId: result.documentId,
        data: {
          ...data,
          updatedBy: userID,
        },
      })

      documentId = responseResult.updateResult.documentId
    }
    else {
      // 未查询到数据，进行创建
      responseResult.insertResult = await strapi.documents(`api::literature.literature`).create({
        data: {
          ...data,
          createdBy: userID,
        },
        status: 'published',
      })
      // 如果发布成功需要退回成未发布状态
      await strapi.documents(`api::literature.literature`).unpublish({
        documentId: responseResult.insertResult.documentId,
      })
      documentId = responseResult.insertResult.documentId
    }
    // 关联normal
    const literatureNormedList = await strapi.documents('api::literature.literature').findOne({
      documentId,
    })
    if (literatureNormedList) {
      await strapi.plugin('extend-admin-api').service('common').formatData({ data: [literatureNormedList], type: 'literature' })
    }

    return responseResult
  },
}))
