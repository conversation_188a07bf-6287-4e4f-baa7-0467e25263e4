const { errors } = require('@strapi/utils')
const OSS = require('ali-oss')

const { ApplicationError } = errors

class OSSClient {
  constructor() {
    this.client = new OSS({
      region: strapi.config.get('oss.region'),
      accessKeyId: strapi.config.get('oss.accessKeyID'),
      accessKeySecret: strapi.config.get('oss.accessKeySecret'),
      bucket: strapi.config.get('oss.bucket'),
    })
  }

  /**
   * 修改名称
   * @returns {Promise<{message: string}>}
   */
  async rename(oldName, newName) {
    try {
      // 先进行复制
      await this.client.copy(newName, oldName)
      // 再删除源文件
      await this.deleteFile(oldName)
      return { message: 'success' }
    }
    catch (error) {
      if (error.code === 'NoSuchKey')
        return { message: 'NoSuchKey' }
      return { message: error }
    }
  }

  /**
   * 判断文件是否存在
   * @returns {Promise<{message: string}>}
   */
  async fileExist(name) {
    try {
      await this.client.head(name)
      return { message: 'success' }
    }
    catch (error) {
      if (error.code === 'NoSuchKey')
        return { message: 'NoSuchKey' }
      return { message: error }
    }
  }

  /**
   * 删除文件
   * @param name
   * @returns {Promise<{message: string}>}
   */
  async deleteFile(name) {
    try {
      await this.client.delete(name)
      return { message: 'success' }
    }
    catch (error) {
      if (error.code === 'NoSuchKey')
        return { message: 'NoSuchKey' }
      return { message: error }
    }
  }

  /**
   * 创建目录
   * @param directoryName
   * @returns {Promise<{message}|{message: string}>}
   */
  async createDirectory(directoryName) {
    try {
      if (!directoryName.includes('/')) {
        directoryName = `${directoryName}/`
      }
      await this.client.put(directoryName, Buffer.from(''))
      return { message: 'success' }
    }
    catch (error) {
      if (error.code === 'NoSuchKey')
        return { message: 'NoSuchKey' }
      return { message: error }
    }
  }

  /**
   * 移动文件
   * 通过type获取移动的来源路径以及目标路径
   * @param target (文献为唯一标识)
   * @param files
   * @returns {Promise<*[]>}
   */
  async moveFiles({ target, files, type, splitCharacters }) {
    const items = Object.assign([], files)
    const contentTypes = strapi.plugin('extend-admin-api').service('common').getContentTypes(type)

    const sourcePathName = `upload_temp/${contentTypes.info.singularName}`
    const targetPathName = `document_${contentTypes.info.singularName}`

    // let uploadFileIDs = []
    const uploadFileResult = []
    const move = async () => {
      if (items.length > 0) {
        const item = items.shift()
        const obj = {}
        // 如果是占位对象，则不进行移动。使用 /static/document/empty.pdf
        if (item.name !== 'empty.pdf') {
          let renameResult = await this.rename(`${sourcePathName}/${item.name}`, `${targetPathName}/${target}/${item.name}`)
          if (renameResult && renameResult.message !== 'success') {
            renameResult = await this.fileExist(`${targetPathName}/${target}/${item.name}`)
            if (renameResult && renameResult.message !== 'success') {
              throw new ApplicationError('未找到文件，请使用上传工具上传。', { data: { name: item.name, ...renameResult } })
            }
          }

          obj.type = item.type ? item.type : null
          if (obj.type === 'PDF') {
            if (!item.page) {
              throw new ApplicationError('PDF文件page不正确', { data: renameResult })
            }
          }
          obj.name = item.name
          obj.oss_path = `${targetPathName}/${target}/`
          obj.size = item.size ? item.size.toString() : ''
          obj.page = item.page ? item.page.toString() : ''
        }
        else {
          obj.name = item.name
          obj.oss_path = '/static/document/empty.pdf'
          obj.size = item.size ? item.size.toString() : ''
          obj.page = item.page ? item.page.toString() : ''
        }

        uploadFileResult.push(`${obj.name}${splitCharacters}${obj.oss_path}${splitCharacters}${obj.size}${splitCharacters}${obj.page}`)
        await move()
      }
    }
    await move()
    return uploadFileResult
  }

  /**
   * 删除文件夹下的所有内容后：如果没有占位对象，文件夹会“消失”。
   * @param folderPath
   * @returns {Promise<*|{message: string}>}
   */
  async deleteFolder(folderPath, type) {
    const contentTypes = strapi.plugin('extend-admin-api').service('common').getContentTypes(type)
    const targetPathName = `document_${contentTypes.info.singularName}`

    // 1.获取文件夹下的所有对象
    const list = await this.client.list({
      prefix: `${targetPathName}/${folderPath}/`,
      delimiter: '',
    })

    if (list.objects && list.objects.length > 0) {
      // 2.构造删除对象列表
      const objectsToDelete = list.objects.map(obj => obj.name)

      // 3.删除所有对象
      return this.client.deleteMulti(objectsToDelete, { quiet: true })
    }
    else {
      return { message: '文件夹中没有内容' }
    }
  }
}

module.exports = OSSClient
