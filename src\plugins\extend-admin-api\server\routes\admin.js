'use strict'

module.exports = {
  type: 'admin',
  routes: [
    {
      method: 'GET',
      path: '/download',
      handler: 'excel.download',
      config: {
        // policies: ['admin::isAuthenticatedAdmin'],
      },
    },
    {
      method: 'POST',
      path: '/upload',
      handler: 'excel.upload',
      config: {
        policies: [
          'admin::isAuthenticatedAdmin',
          // {
          //   name: 'admin::hasPermissions',
          //   config: {
          //     actions: ['plugin::upload.assets.create'],
          //   },
          // },
        ],
      },
    },
    {
      method: 'POST',
      path: '/normalize',
      handler: 'common.formatData',
      config: {
        // policies: [
        //   'admin::isAuthenticatedAdmin',
        //   {
        //     name: 'admin::hasPermissions',
        //     config: {
        //       actions: ['plugin::upload.assets.create'],
        //     },
        //   },
        // ],
      },
    },
  ],
}
