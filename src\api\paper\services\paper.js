'use strict'
const { errors } = require('@strapi/utils')
/**
 * paper service
 */
const knex = strapi.db.connection
// const UtilOSS = require('../../../utils/oss')
const IPv4CidrRange = require('ip-num').IPv4CidrRange

const { createCoreService } = require('@strapi/strapi').factories

const { ApplicationError } = errors

module.exports = createCoreService('api::paper.paper', ({ strapi }) => ({
  /**
   * 论文主页类别统计
   * @returns {Promise<*>}
   */
  async categoryStatistics() {
    const splitFields = ['degree_conferring_time', 'discipline', 'degree_type', 'training_unit', 'is_good_paper', 'language_of_work', 'call_string', 'keywords', 'professional_field_navigation']

    const splitFieldResult = await this.groupByMultipleSplitFields('papers', splitFields)

    const callStringMap = await strapi.documents(`api::enum-paper-call-string.enum-paper-call-string`).findMany({
      fields: ['name', 'identifier'],
      filters: {
        identifier: { $in: splitFieldResult.callString.map(item => item.callString) },
      },
    })

    const professionalFieldNavigationsMap = await strapi.documents(`api::enum-paper-professional-field-navigation.enum-paper-professional-field-navigation`).findMany({
      fields: ['name', 'code'],
      filters: {
        code: { $in: splitFieldResult.professionalFieldNavigation.map(item => item.professionalFieldNavigation) },
      },
    })

    // 替换
    splitFieldResult.callString = this.applyCodeMap(splitFieldResult.callString, 'callString', 'identifier', callStringMap)
    splitFieldResult.professionalFieldNavigation = this.applyCodeMap(splitFieldResult.professionalFieldNavigation, 'professionalFieldNavigation', 'code', professionalFieldNavigationsMap)
    return { data: splitFieldResult }
  },

  /**
   * 通过多对多关联字段进行分组统计
   * @param table
   * @param fields
   * @returns {Promise<{}>}
   */
  async groupByMultipleSplitFields(table, fields) {
    const rows = await knex(table).select(...fields).whereNotNull('published_at')

    const counters = {}

    // 初始化计数器
    for (const field of fields) {
      counters[field] = {}
    }

    for (const row of rows) {
      for (const field of fields) {
        const rawValue = row[field]
        if (!rawValue)
          continue

        const values = rawValue
          .split(';')
          .map(v => v.trim())
          .filter(v => v.length > 0)

        for (const value of values) {
          counters[field][value] = (counters[field][value] || 0) + 1
        }
      }
    }

    // 格式化输出
    const result = {}

    for (const field of fields) {
      const outputKey = this.camelCase(field) // 可选转驼峰（如 discipline_normal => disciplineNormal）
      result[outputKey] = Object.entries(counters[field]).map(([val, count]) => ({
        [outputKey]: val,
        count,
      })).sort((a, b) => b.count - a.count)
    }

    return result
  },

  camelCase(str) {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
  },

  applyCodeMap(dataList, keyName, VerifyName, map) {
    return dataList.map((item) => {
      const rawValue = item[keyName]
      const mapItem = map.find(item2 => item2[VerifyName] === rawValue)
      const mappedValue = mapItem ? mapItem.name : rawValue
      return {
        [keyName]: mappedValue,
        count: item.count,
      }
    })
  }, /**
      * 修改文件
      * @returns {Promise<void>}
      */
  // async updateFile() {
  //   const documentIds = ['rur8fks9inrunus7jn128ph5', 'dnajqoir3f6q322bw5zfjvcr', 'ejwg9282zepk9wdh1bomh2k8', 't3exvzdll8rsrom3qszcpa4e', 'o3dtqrnp6bynwop84pw47cwi', 'om7qtcbue5u3f243kux47a45', 'zh2pkscjldvq5wfz5b0r2m95', 'w39p671cxufmwtynreal7wsx', 'xz5b5f2nupybyxrihfgt2w4c', 'nko76fvd5ic5qa5vjl74o70q', 'nxnqsg9cqq4r6qqrkw8h1wih', 'ltryekm30lvcer1zoky6m9q5', 'hrwx279le2oxk27j1wqjf2ak', 'mlblmdn6dbbh1wtnoqsurnu9', 'coksmhr3wkrbhfzodioydxbv', 'rt90bfkbma9fhh517y6dkhx4', 'ex7u5e8klsm0j5ak6kjachol', 'hbccblke51akz3wsc3pgf3mb', 'rm40sjdio72i0viklwaokunm', 'v79uxuqluklvbwfx7zoeexat', 'six3j415selkki7whtgqh9sd', 'yg4kyux149u4i8at4z21d77s', 'hxnhoj62r7zoqj02gbzn76u3', 'z7a7p4c7f0uh5q8gci3dnkse', 'iyjg4jup4yf28gutah7j9rvt', 'd1q0k8ysjfl4o9j4uuhnbpuy', 'vnkxiyzqt3tmw7alqlseijvf', 'u4hlc9be9hcjxkkeoac1pdhe', 'pu75n0c77tfo1qdj1jb1m3tw', 'hjbaed5zhr46wkq58lunii0m', 'zq15epvfaywxnegov40qsgia', 'a8aibhbwgcjxcwddbb1c8ma1', 'lew6hasgxjdxmokr6rdife5g', 'spmoh8509shdi1cut0dbhbh0', 'ygsrg9dypn6hl12634217355', 'qumtiu7k0docsw65tubw3a44', 'ovcnkjxtqkkf1szpnhilnxwk', 'i8j9wj0izpi4if5f9rmcuwwz', 'l6occ5u3k45g6kr0z0xmst26', 'wb5d8s8lwdozvct3euwzeqsk', 'x1xs6nlxr4htur7sqiw8ln1l', 'nvm98bu351ov4aqkz09lrh6w', 'rxo8ska1o4ratd68yyc8ma9u', 'yfq1su62rsfgynxx84x6kznp', 'd4fjytk2oe756if36vxz5hg8', 'scazgfkxx7zv0y0epi0jjsl0', 'y65yw14k0fhp356c3csxm8o5', 'w4388vlo2s0r9rylhcnct81u', 'uwgqjdyliei8f08epkxvk0c0', 'mpqlgvmifpgv1nsd0gijzog7', 'euq26348jzc77j9vggmljabg', 'u9qxecvx4860f1hm5rurcxj1', 'im36l2bivjxfham2mg2sgk1q', 'uvasju8fl1e112cq0q0s3nwa', 'hmgjxjm9gvv2j4yunipn4ozc', 'koopv22ubx8evkgxt2ucrqqs', 'ejdrkd9mcfobj81eiv5ts6hf', 'w4yk07tz1x4oox6d9fncphqk', 'yx64ir49pnfz83ws5sniqlyg', 'eafn32hfamsg87c1gre75vdh', 'rbsrj8xu3bnty06gzxoic01i', 'z2x94uxzno049ltcm3v8rpw9', 'e4avvq0jo0woex022zk3g74e', 'araru9v1r9etm83lxzkd7fb8', 's7yjc7x05uncbe4y0snuovid', 'id09lxi25icmfrq2igc4ljoh', 'zxz3ry9hhfwuyy1bmwkvshm2', 'cprvwl3y1on0rcuwdh9zheqh', 'y6a0ujv7q96ltcpaidm0nosj', 'mg5rwzay38zspkcuqjix4gxv', 'k332h8tb1e9csee2s5az66x7', 'k7rr4onpwft8z7667lq96b32', 'km178m8d3hqqotng86g5tmf2', 'k9fw270rx1tklue3wfqg0zfo', 'nfjfdcdz1o8wbdwtepolim42', 'fxfegpqvif7wa3d6lygvpsoz', 'gthq3ltqsr2gpl2e03u4au0i', 's7kxbm1a63mmh8rbfzfsl4wf', 'y1727mz7px3vra2sbhjdu6do', 'yxi8l6de3qvthxdl3oww1dyw', 'ajsnasanzxgagv31x1rd2uc1', 'sh9d8ykdimw47aq7g9yj9r0h', 'qldiu67hd58fqgv5739zxma6', 'iw3vaz72is5ij7p2zdjcgyok', 'iw1wh8rlxmszbv6su89r2mpt', 'pkhnz2kczmnhnllxwy35w6xt', 'gmd39ov6rkhmnzdwifofbizk', 'tubpdt1e08j76rxgz8a9m9te', 'm89t1up3q42gui3x3f62bd1i', 'dum9rlgqgbxftyzhcwb2yx6r', 'uocls2iji5xjuesv8qz9zo43', 'by17lnvnheyceojreq9mpwt5', 'chq66hjqmsdmoa6mqsp38toy', 'gwr2u2k4lrrk5acpx9xcwg4b', 'smxkp99wfo6r41r5ch3z76dg', 'opfxoizihkbm4k6e2tv3x6qh', 'n76ev3vsb3xyonbngykaab8n', 'fei67acuqza1cv6gcvlr0fy7', 't0q1vfy48yo7bze1b54302aj', 'yxiuvto4ukiy2gbegmcfewcy', 'ga1zi7mgd9aq4wjukrqvok2c', 'tj8k3x7g1ggxjgiyglx6zyfl', 'rgc6axv86mifcaqlbqk9pj79', 'fz7h95z9bu9idrddmu5d6ahr', 'tq1yxniwxtgkluikpfajnrjb', 'aox5wrkozyllx18d5esc1wus', 'q26lpzd90f02mmnqtaat2ho4', 'abg4evz8zoeyj3tkransy02p', 'ybylhqniwd3f30kxdtv1lg9i', 'mkxbbvi1uw697tth2m3e6g0j', 'czgkufea9kvsf2s6jliz5u8i', 'gsh2496j92vi3tgefyei530f', 'k7vf5rr54hzqsymhm6s7oxz2', 'rvekxolbyxzw371kfnnn5qs5', 'uycj7qbfnb8kbnshhuk6nk6m', 'cdtri1w4ew0980s476q3g5v1', 'ko9dhjypjhoss12jjm6t5h2b', 'lw3i7yn5g0gu08sd13c9baij', 'dq0j9fu4di5xiptfz4rrmp0g', 'ymnxbo3vu75kvwj6h6klvu66', 's9xeyhxeaiuser3iq49bk1a2', 'iaer84dnybl4rzg69880qd8f', 'qgzflcslykksvdk4ti5bo2uf', 'e67ujsbuezwewgmswa4kshq6', 'o6k7q9n6kd6eph7kze0ffu16', 'sgrzmj2eo9fuj3bk3758nis5', 'beyza1ecxx2vmo37w3okjj05', 'pk4jf4zm06mgv4zs2waqplsy', 'vyo320rsrd18p2b2h6fd564e', 'l72p96eptqj0qavjpaw14bs0', 'copvze0sfgo8kacd0keqj6or', 'ylof74tdj973fin9m5gcw269', 'bg4wfcf8ckz0m1ejxu65o3xe', 'kdy2x3candx3vqyfyw5pxrym', 'o46cstb9vpy7uqj4fe8mj6sa', 'flfic1ejqdnoat0da0c1cnla', 'qkavrffokvdjr1lcz5cpya08', 'o8789urwwo3dq1nnxvp19nsj', 'cxkpbhllvl54dan2qwosybrx', 'qxu1brodbdendyw6fhysz27b', 'di5ko53m3x5nd3jmqy150r4v', 'h5g5ia5ptg3n4616k6dvfb7j', 'zz2wies9jj1uwr3uchj5ble2', 'tdbfsns1dzxj3o67wpjd3mfd', 'mgx1ik5tt68tgev2pxdqok1k', 'ytuf7k6ca5myvbxnup5wxd4r', 'oejl9hj5jyptkl2evkor7r04', 'azcouh3rrippqrzb3yov7mvy', 'cwubff9kdn5cubh0jlcyqncn', 'cldhuakv7estcz2apqaqb4ye', 'ut5uc5fy8kqqwux091d0bkbj', 'kb73q87sxrtfany9hgckkyjm', 'fs92p939bsjggx8br7fptwln', 'im472g719g0k763qrakzkole', 'c2xffqlqrn2oggkk5eir4wx5', 'rjuguvm5zcqqqop42jkc1esf', 'ee32x72i1unti1e6yvc0tzlm', 'jfy4aqapbsabvngghhp8m6sd', 'al8lir2zb0a7vh7wbx7id3k2', 'b4gv4a345ol7jmf4qprj1uot', 'bj94ry1t9yp3w70owgdckt7p', 'yhjfqwr66d9wu4hu3l4zd8pk', 'koebgsi0vavc858qlil5e0fa', 'v8p2l0933xgr5ytweylq4xfp', 'egawxr2baj3z0g37duxn7o7e', 'fu0rsovw2kjuxecmnizn5xo8', 'gny98iejfqyibwnl00b18rvo', 'd2sscdr2lqghsimhoxkol934', 's11n6yhyvqn1urra2uvhuo08', 'zmsbie5l2lg6fvptgtp8si9u', 'myv4wv093h6csmmzbegsnwus', 'uubbdsh3wdr5v7aaprw0n0xr', 'sg2yhiws1yefy0w7i4qu7dqj', 'fn9pggg8tqeu92vr9rpvhgu5', 'sxgh3sxwrdo3q53wjsuvbpne', 'ofpyar6z2mfki4pkyccu5x99', 'xr9sayz9a1ihlmg9brmgaauq', 'bjwz6vxowrjb6p3smbzxrbgw', 'iufb08lnh242il6ujg9op3wb', 'dskexdebc191o4aogd6rodj4', 'zzrvgjmm2ae1hxzk0bmxyhjd', 'pn0t2iglpaemva0ha0u161zw', 'amo21ye7jpiwrqm8g84yko11', 'wek7w4xl8ro740cz88tbquxk', 'mefiofoup3lsuhvvit2nfd0t', 'a012i8lmskdr2jjr5axuf6oq', 'zrz8um8wypmdunod87c1f8wo', 'lhd8mjdnm74vqyybwdcl6ywi', 'hfpil200j7m7irs3jp24ewkl', 'xs0i0hths4dxusk1lds6n8df', 'i543qbdgs4umts8k1lksji31', 'vo9tb1jwwnuguhxu10qtp4pm', 'e3me9uw2x2vob90fj5m16gmd', 'nsewhyh17yua13sookc6j75s', 'pfh0hltgis3becugff4vzq72', 'byyt49ugrx0wru7xa19650oe', 'gdoqqc2bie58br7kz9bwdo6r', 'ht6hqqq996kshljq1cvrhi3j', 'm97xzqksh99hx4qv8fg0fvan', 'ebyq6rbwzsls1o7s92xzlay7', 'l7v0dxclvv9vmwh41gm8wmmu', 'kca6q3jqegjx96l3f848ar05', 'y4jysisgkpjvaiom7vqtfn7s', 'n1cetocrqmpi5uv2sclrfkul', 'imst6ew1fnd9eoefgvzuxzo0', 'ezgazi6q2axwua8a1hrspr6z', 'fhwuyfdb8e7s8ukzxpmju7p7', 'zggwlm3s7xqz8oeb4s90ie9r', 'sl6jrbvnwvpecn7hi1mjiatv', 'flk7sbhswzir8v3hota0m7zt', 'nehpbhdv77lupvuta8h8z6dz', 'u376uk6yiknbpl7pff7eo5km', 'mm3mvnsybug4gc6xl8ucjqpy', 'gkn3fuitckk3153sk5rthq73', 'j0iwdvxdbd9als2hthtwbmv0', 'v5adzci71amu58i49po6llrh', 'bp14prwwlstwwznfet2qsn7w', 'ds7cu49qepwamse6l8k0j8f6', 'elko31wjog7uhmy5xt0140v6', 'j6juc4xgsmtri2xpyy09dwjm', 'avqxy9hsb3dhhdvidj47donn', 'bactbz3ytge3zqiir4m6vqtx', 'amy3k0h9iapeawitvl6o6ix2', 'l6h9rwf55s4sb482xgx9kgx7', 'n7bl7ei8a70cdg3bjoh1xnfi', 'repfi17la42lbzra0ypm3plw', 'yc17baamfifi8ybqbvwh16wt', 'b9jrn047gb5pm4hxnfpxsb58', 'f2d1jhcsie2xprtxl305pjsl', 'o9blq9lgdd0v2ih2ez45wjdu', 'ngmpwsw16u13d2b9z4u8gyoz', 'auplepfymyxetnzm2feqxo2a', 'ip81d1zrim6bcp047bu1wfd9', 'kqnoauropqvvszp77c6gwt3x', 'eesu4z1qvojhkrtucxpw1n32', 'iltp7xc6f8n4ev4885iomuiw', 'vcueo4ly7jukyr1ul0wkkzt5', 'jecsqhui1628xtd02ltj5phi']
  //   // const documentIds = ['v2zxxzba30m87q3alqiopy69']
  //   const handlerPaper = async () => {
  //     if (documentIds.length > 0) {
  //       const item = documentIds.pop()
  //       await knex('papers')
  //         .where({ document_id: item })
  //         .update({
  //           pdf_all_file: 'empty.pdf',
  //           pdf_all_file_normal: 'empty.pdf|-|/static/document/empty.pdf|-||-|',
  //           pdf_pre_file: 'empty.pdf',
  //           pdf_pre_file_normal: 'empty.pdf|-|/static/document/empty.pdf|-||-|',
  //         })
  //         .then((v) => {
  //           strapi.documents('api:paper.paper').publish({
  //             documentId: item,
  //           })
  //         })
  //         .catch((error) => {
  //           strapi.log.error(`更新论文文件名失败: ${item}`, error)
  //         })
  //       await handlerPaper()
  //     }
  //   }
  //   await handlerPaper()
  //   // 查询所有论文，筛选出没有正确文件名的记录
  //   // const papers = await strapi.documents(`api::paper.paper`).findMany({
  //   //   filters: {
  //   //     $or: [{ pdf_all_file: { $notContains: 'pdf_all' } }, { pdf_pre_file: { $notContains: 'pdf_pre' } }],
  //   //   },
  //   // })
  //   // const utilOSSInstance = new UtilOSS()
  //   // // 遍历每个论文，更新文件名
  //   // const paperPromise = papers.map(async (paper) => {
  //   //   const updatedData = {}
  //   //
  //   //   if (paper.pdf_all_file && !paper.pdf_all_file.includes('pdf_all')) {
  //   //     const newPDFAllFile = `pdf_all_${paper.identifier}.pdf`
  //   //     // 修改oss
  //   //     await utilOSSInstance.rename(`document_paper/${paper.identifier}/${paper.pdf_all_file}`, `document_paper/${paper.identifier}/${newPDFAllFile}`)
  //   //     updatedData.pdf_all_file_normal = paper.pdf_all_file_normal.replace(paper.pdf_all_file, newPDFAllFile)
  //   //     updatedData.pdf_all_file = newPDFAllFile
  //   //   }
  //   //
  //   //   if (paper.pdf_pre_file && !paper.pdf_pre_file.includes('pdf_pre')) {
  //   //     const newPDFPreFile = `pdf_pre_${paper.identifier}.pdf`
  //   //     // 修改oss
  //   //     await utilOSSInstance.rename(`document_paper/${paper.identifier}/${paper.pdf_pre_file}`, `document_paper/${paper.identifier}/${newPDFPreFile}`)
  //   //     updatedData.pdf_pre_file_normal = paper.pdf_pre_file_normal.replace(paper.pdf_pre_file, newPDFPreFile)
  //   //     updatedData.pdf_pre_file = newPDFPreFile
  //   //   }
  //   //
  //   //   // 更新数据库
  //   //   if (Object.keys(updatedData).length > 0) {
  //   //     await knex('papers')
  //   //       .where({ id: paper.id })
  //   //       .update(updatedData)
  //   //       .catch((error) => {
  //   //         strapi.log.error(`更新论文文件名失败: ${paper.id}`, error)
  //   //       })
  //   //     console.log()
  //   //   }
  //   // })
  //   // await Promise.all(paperPromise)
  // },

  /**
   * 查询PDF文件
   * @param ctx
   * @returns {Promise<void>}
   */
  async validatePermissions(ctx) {
    const clientIP = ctx.request.ip
    const { documentId } = ctx.query
    const contentTypes = strapi.plugin('extend-admin-api').service('common').getContentTypes('paper')
    const ipArr = strapi.config.get('white-ip-list').ipRanges
    let ipRanges = []
    ipArr.forEach((v) => {
      ipRanges = ipRanges.concat(v.ipArr)
    })
    if (!this.isIpInRange(clientIP, ipRanges)) {
      throw new ApplicationError('无权限')
    }
    const paper = await strapi.documents(`api::paper.paper`).findOne({
      fields: ['pdf_all_file_normal'],
      documentId,
    })
    const splitCharacters = contentTypes.attributes.pdf_all_file.extRelatedTable.splitCharacters
    if (!paper || !paper.pdf_all_file_normal || !paper.pdf_all_file_normal.split(splitCharacters)[1]) {
      throw new ApplicationError('未找到论文')
    }
    return null
  },

  /**
   * 检查IP是否在指定的范围内
   * @param ip
   * @param rangeList
   * @returns {*}
   */
  isIpInRange(ip, rangeList) {
    return rangeList.some((range) => {
      const localIPRange = IPv4CidrRange.fromCidr(`${ip}/32`)
      const cidr = IPv4CidrRange.fromCidr(range)
      return cidr.inside(localIPRange)
    })
  },

  /**
   * 访问统计
   * @param ctx
   * @returns {Promise<void>}
   */
  async accessStatistics(ctx) {
    const { status, documentId } = ctx.request.body
    if ([1, 2].includes(status)) {
      const paper = await strapi.documents(`api::paper.paper`).findOne({
        documentId,
      })
      if (status === 1) {
        paper.download_count = paper.download_count ? paper.download_count : 0 + 1
      }
      if (status === 2) {
        paper.view_count = paper.view_count ? paper.view_count : 0 + 1
      }
      await strapi.documents('api::paper.paper').update({
        documentId,
        data: paper,
      })
      await strapi.documents('api::paper.paper').publish({
        documentId,
      })
    }
    return null
  },
}))
