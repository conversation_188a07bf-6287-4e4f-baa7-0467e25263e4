{"kind": "collectionType", "collectionName": "papers", "info": {"singularName": "paper", "pluralName": "papers", "displayName": "学位论文库/元数据", "description": ""}, "options": {"draftAndPublish": true}, "extUniqueField": ["identifier"], "extMd5Field": ["title", "title_en", "author", "degree_conferring_time"], "extIsStandardizedData": true, "attributes": {"identifier": {"type": "string", "regex": "^(?!\\s*$).+", "required": true, "unique": true, "extExcelTitle": "唯一标识"}, "unique_md5": {"type": "string", "extIsFieldExported": false}, "title_en": {"type": "text", "required": true, "extExcelTitle": "题名（英文）", "regex": "^(?!\\s*$).+"}, "title": {"type": "text", "required": true, "extExcelTitle": "题名（中文）", "regex": "^(?!\\s*$).+"}, "author": {"type": "string", "required": true, "extExcelTitle": "作者", "regex": "^(?!\\s*$).+"}, "keywords": {"type": "text", "required": true, "extExcelTitle": "关键词", "regex": "^(?!\\s*$).+"}, "collection_string": {"type": "string", "extExcelTitle": "馆藏号"}, "control_number": {"type": "string", "extExcelTitle": "控制号"}, "call_number": {"type": "string", "extExcelTitle": "索书号"}, "call_string": {"type": "string", "extExcelTitle": "指导老师", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "call_string_normal", "tableName": "enum-paper-call-string", "separator": ";", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["identifier"]}}, "call_string_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "training_unit": {"type": "string", "extExcelTitle": "培养单位", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "training_unit_normal", "tableName": "enum-paper-training-unit", "separator": ";", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "training_unit_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "collection_institution": {"type": "string", "required": true, "extExcelTitle": "收藏单位", "regex": "^(?!\\s*$).+"}, "awarding_body": {"type": "string", "required": true, "extExcelTitle": "授予机构", "regex": "^(?!\\s*$).+"}, "is_good_paper": {"type": "string", "extExcelTitle": "是否优秀论文"}, "discipline": {"type": "string", "extExcelTitle": "学科", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "discipline_normal", "tableName": "enum-paper-discipline", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "discipline_normal": {"type": "string", "isFieldExported": false, "isValidationRequired": false}, "application_degree_type": {"type": "string", "required": true, "extExcelTitle": "申请学位类型", "regex": "^(?!\\s*$).+"}, "degree_type": {"type": "string", "required": true, "extExcelTitle": "学位类型", "regex": "^(?!\\s*$).+", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "degree_type_normal", "tableName": "enum-paper-degree-type", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "degree_type_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "degree_conferring_time": {"type": "string", "regex": "\\d{4}", "required": true, "extExcelTitle": "学位授予时间"}, "professional_field_entry": {"type": "string", "required": true, "extExcelTitle": "专业/领域（条目）", "regex": "^(?!\\s*$).+"}, "professional_field_navigation": {"type": "string", "required": true, "extExcelTitle": "专业/领域（导航）", "regex": "^(?!\\s*$).+", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "professional_field_navigation_normal", "tableName": "enum-paper-professional-field-navigation", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "professional_field_navigation_normal": {"type": "string", "isFieldExported": false, "isValidationRequired": false}, "research_direction": {"type": "string", "required": true, "extExcelTitle": "研究方向", "regex": "^(?!\\s*$).+"}, "page": {"type": "string", "required": true, "extExcelTitle": "页数", "regex": "^(?!\\s*$).+"}, "language_of_work": {"type": "string", "extExcelTitle": "语种"}, "clc": {"type": "string", "extExcelTitle": "中图分类"}, "remarks": {"type": "string", "extExcelTitle": "备注"}, "download_count": {"isFieldExported": false, "type": "integer"}, "view_count": {"isFieldExported": false, "type": "integer"}, "pdf_abstract": {"type": "text", "extExcelTitle": "PDF摘要（中文）"}, "pdf_abstract_en": {"type": "text", "extExcelTitle": "PDF摘要（英文）"}, "pdf_all_file": {"type": "string", "extExcelTitle": "PDF文件名（全文）", "extRelatedTable": {"associatedFieldName": "pdf_all_file_normal", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:"}}, "pdf_all_file_normal": {"type": "text", "extIsFieldExported": false, "extIsValidationRequired": false}, "pdf_pre_file": {"type": "string", "extExcelTitle": "PDF文件名（18页）", "extRelatedTable": {"associatedFieldName": "pdf_pre_file_normal", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:"}}, "pdf_pre_file_normal": {"type": "text", "extIsFieldExported": false, "extIsValidationRequired": false}, "normed": {"type": "boolean", "default": false}}}