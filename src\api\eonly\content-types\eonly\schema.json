{"kind": "collectionType", "collectionName": "eonlies", "info": {"singularName": "eonly", "pluralName": "eonlies", "displayName": "多业务库/E-ONLY资源", "description": ""}, "options": {"draftAndPublish": false}, "extUniqueField": ["identifier"], "extMd5Field": ["title", "author"], "attributes": {"abstract": {"extExcelTitle": "摘要", "type": "text"}, "author": {"extExcelTitle": "作者", "type": "text"}, "author_org": {"extExcelTitle": "作者机构", "type": "text"}, "detailurl": {"extExcelTitle": "采集网址", "type": "text"}, "doi": {"extExcelTitle": "DOI", "type": "string"}, "eissn": {"extExcelTitle": "电子刊号", "type": "string"}, "files": {"type": "text", "extExcelTitle": "pdf文件名", "extRelatedTable": {"associatedFieldName": "files_normal", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:"}}, "files_normal": {"type": "text", "extIsFieldExported": false, "extIsValidationRequired": false}, "identifier": {"extExcelTitle": "PID", "type": "string"}, "unique_md5": {"type": "string"}, "issn": {"extExcelTitle": "刊号", "type": "string"}, "issue": {"extExcelTitle": "期", "type": "string"}, "keyword": {"extExcelTitle": "关键词", "type": "text"}, "order_id": {"extExcelTitle": "订购号", "type": "string"}, "pdf_file_path": {"extExcelTitle": "pdf文件目录", "type": "text"}, "pub_year": {"extExcelTitle": "年", "type": "string"}, "publisher": {"extExcelTitle": "出版者", "type": "string"}, "source_title": {"extExcelTitle": "刊名", "type": "string"}, "start_page": {"extExcelTitle": "起页", "type": "string"}, "end_page": {"extExcelTitle": "止页", "type": "string"}, "page_num": {"extExcelTitle": "总页数", "type": "string"}, "subtitle": {"extExcelTitle": "副题名", "type": "text"}, "title": {"extExcelTitle": "题名", "type": "text"}, "volume": {"extExcelTitle": "卷", "type": "string"}}}