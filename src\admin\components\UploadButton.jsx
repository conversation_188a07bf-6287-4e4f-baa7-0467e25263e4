import { UploadOutlined } from '@ant-design/icons'
import { Button, message } from 'antd'

import * as React from 'react'
import { useParams } from 'react-router-dom'
import { download, post } from '../common/fetch'

function UploadButton() {
  const params = useParams()
  // return (<div>sss</div>)

  const inputRef = React.useRef(null)
  // const [dialog, setDialog] = useState({ visible: false, details: [], title: '' });
  const [isLoading, setIsLoading] = React.useState(false)

  const handleClick = (e) => {
    e.preventDefault()
    if (inputRef.current) {
      inputRef?.current.click()
    }
  }

  const handleChange = async (e) => {
    const target = e.target
    const file = target?.files[0]
    const singularName = params.slug.split('.')[1]

    const formData = new FormData()

    if (file) {
      formData.append('file', file)
    }
    formData.append('type', singularName)

    setIsLoading(true)

    try {
      const res = await post('/extend-admin-api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      // await refetch()



      message.success(`上传成功，新增${res.data?.data?.insertResult?.length}条，修改${res.data?.data?.updateResult?.length}条`)
    }
    catch (err) {
      if (err.response?.data?.error?.details?.filePath) {
        download(err.response.data.error.details.filePath, {
          filename: `${singularName}-export`,
        })
      }

      message.error(err?.response?.data?.error?.message || err.message || '上传失败')
    }
    finally {
      target.value = ''
      setIsLoading(false)
    }
  }

  return (
    <div>
      <input
        type="file"
        ref={inputRef}
        onChange={handleChange}
        style={{ opacity: 0, position: 'absolute', zIndex: -1 }}
      />

      <Button loading={isLoading} icon={<UploadOutlined />} onClick={handleClick}> 导入</Button>
    </div>
  )
}

export { UploadButton }
