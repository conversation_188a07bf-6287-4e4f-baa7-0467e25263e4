class FetchError extends Error {
  constructor(message, response) {
    super(message)
    this.name = 'FetchError'
    this.message = message
    this.response = response
    this.code = response?.data?.error?.status
    this.status = response?.data?.error?.status

    // Ensure correct stack trace in error object
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, FetchError)
    }
  }
}

const STORAGE_KEYS = {
  TOKEN: 'jwtToken',
  USER: 'userInfo',
}

function getCookieValue(name) {
  let result = null
  const cookieArray = document.cookie.split(';')
  cookieArray.forEach((cookie) => {
    const [key, value] = cookie.split('=').map(item => item.trim())
    if (key === name) {
      result = decodeURIComponent(value)
    }
  })
  return result
}

function getToken() {
  // const fromLocalStorage = localStorage.getItem(STORAGE_KEYS.TOKEN);
  // 兼容5.11.3
  const fromLocalStorage = sessionStorage.getItem(STORAGE_KEYS.TOKEN)
  if (fromLocalStorage) {
    return JSON.parse(fromLocalStorage)
  }

  const fromCookie = getCookieValue(STORAGE_KEYS.TOKEN)
  return fromCookie ?? null
}

const isFormDataRequest = body => body instanceof FormData
const addPrependingSlash = url => (url.charAt(0) !== '/' ? `/${url}` : url)

// This regular expression matches a string that starts with either "http://" or "https://" or any other protocol name in lower case letters, followed by "://" and ends with anything else
const hasProtocol = url => /^(?:[a-z+]+:)?\/\//i.test(url)

// Check if the url has a prepending slash, if not add a slash
const normalizeUrl = url => (hasProtocol(url) ? url : addPrependingSlash(url))

function addBaseUrl(url) {
  // @ts-ignore
  return `${window.strapi.backendURL}${url}`
}

async function responseInterceptor(response) {
  try {
    const result = await response.json()

    /**
     * validateStatus allows us to customize when a response should throw an error
     * In native Fetch API, a response is considered "not ok"
     * when the status code falls in the 200 to 299 (inclusive) range
     */
    if (!response.ok && result.error) {
      throw new FetchError(result.error.message, { data: result })
    }

    if (!response.ok) {
      throw new FetchError('服务异常')
    }

    return { data: result }
  }
  catch (error) {
    if (error instanceof SyntaxError && response.ok) {
      // Making sure that a SyntaxError doesn't throw if it's successful
      return { data: [], status: response.status }
    }
    else {
      throw error
    }
  }
}

async function download(url, options) {
  const headers = new Headers({
    'Accept': 'application/octet-stream',
    'Authorization': `Bearer ${getToken()}`,
    'Content-Type': 'application/json',
    ...options?.headers,
  })

  const response = await fetch(addBaseUrl(normalizeUrl(url)), {
    method: 'GET',
    headers,
  })

  if (!response.ok) {
    throw new FetchError('下载失败', response)
  }

  let filename = options?.filename
  if (!filename) {
    const disposition = response.headers.get('content-disposition')
    if (disposition && disposition.includes('filename=')) {
      const filenameMatch = disposition.match(/filename=["']?([^"']+)["']?/)
      if (filenameMatch?.[1]) {
        filename = decodeURIComponent(filenameMatch[1])
      }
    }
  }

  if (!filename) {
    filename = `download-${Date.now()}`
  }

  try {
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = filename

    document.body.appendChild(a)
    a.click()

    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }
  catch (error) {
    throw new FetchError('文件下载处理失败', error)
  }
}

async function post(url, data, options) {
  const headers = new Headers({
    'Accept': 'application/json',
    'Authorization': `Bearer ${getToken()}`,
    'Content-Type': 'application/json',
    ...options?.headers,
  })

  if (isFormDataRequest(data)) {
    headers.delete('Content-Type')
  }

  const response = await fetch(addBaseUrl(normalizeUrl(url)), {
    method: 'POST',
    headers,
    body: isFormDataRequest(data) ? data : JSON.stringify(data),
  })

  return responseInterceptor(response)
}

async function get(url, options) {
  const headers = new Headers({
    'Accept': 'application/json',
    'Authorization': `Bearer ${getToken()}`,
    'Content-Type': 'application/json',
    ...options?.headers,
  })

  const response = await fetch(addBaseUrl(normalizeUrl(url)), {
    method: 'GET',
    headers,
  })

  return responseInterceptor(response)
}

export { download, get, post }
