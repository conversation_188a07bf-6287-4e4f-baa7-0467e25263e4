{"kind": "collectionType", "collectionName": "enum_paper_training_units", "info": {"singularName": "enum-paper-training-unit", "pluralName": "enum-paper-training-units", "displayName": "学位论文库/规范-培养单位"}, "options": {"draftAndPublish": false}, "extUniqueField": ["name", "code"], "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true, "extExcelTitle": "中文名称", "regex": "^(?!\\s*$).+"}, "name_en": {"type": "string", "extExcelTitle": "英文名称"}, "code": {"type": "string", "unique": true, "extExcelTitle": "标准代码"}, "name1": {"type": "string", "extExcelTitle": "名称1"}, "name2": {"type": "string", "extExcelTitle": "名称2"}, "name3": {"type": "string", "extExcelTitle": "名称3"}, "name4": {"type": "string", "extExcelTitle": "名称4"}, "name5": {"type": "string", "extExcelTitle": "名称5"}, "name6": {"type": "string", "extExcelTitle": "名称6"}, "name7": {"type": "string", "extExcelTitle": "名称7"}, "name8": {"type": "string", "extExcelTitle": "名称8"}, "name9": {"type": "string", "extExcelTitle": "名称9"}}}