module.exports = ({ env }) => ({
  // redis 暂不支持
  // 'redis': {
  //   config: {
  //     connections: {
  //       default: {
  //         connection: {
  //           host: env('REDIS_HOST', '127.0.0.1'),
  //           port: env('REDIS_PORT', '6379'),
  //           db: env('REDIS_DB', 0),
  //           password: env('REDIS_PASSWORD', 'MAW5F4VQRWS56HU8151XE'),
  //         },
  //         settings: {
  //           debug: false,
  //         },
  //       },
  //     },
  //   },
  // },
  // 'rest-cache': {
  //   config: {
  //     provider: {
  //       name: 'redis',
  //       options: {
  //         max: 32767,
  //         connection: 'default',
  //       },
  //     },
  //     strategy: {
  //       debug: true,
  //       keysPrefix: 'api_cache',
  //       contentTypes: [
  //         // 'api::paper.paper',
  //       ],
  //     },
  //   },
  // },
  'upload': {
    config: {
      sizeLimit: 1024 * 1024 * 1024, // 256mb in bytes
    },
  },
  'extend-admin-api': {
    enabled: true,
    resolve: './src/plugins/extend-admin-api',
  },
  'elasticsearch': {
    enabled: true,
    config: {
      indexingCronSchedule: '00 23 * * *',
      searchConnector: {
        host: env('ELASTICSEARCH_HOST'),
        username: env('ELASTICSEARCH_USERNAME'),
        password: env('ELASTICSEARCH_PASSWORD'),
        // certificate: '<path to the certificate required to connect to Elasticsearch>',
      },
      indexAliasName: env('ELASTICSEARCH_INDEX_ALIAS_NAME'),
    },
  },
})
