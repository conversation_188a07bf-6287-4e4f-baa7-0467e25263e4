import { download, post } from '../common/fetch'
import { PartitionOutlined } from '@ant-design/icons'
import * as React from 'react'
import { Button, Popconfirm, message } from 'antd';
import { useParams } from 'react-router-dom'

function NormaliseButton() {
    const params = useParams()

  const [isLoading, setIsLoading] = React.useState(false)
  const singularName = params.slug.split('.')[1]
  if (singularName !== 'literature' && singularName !== 'paper')
    return null

  const onConfirm = async (e) => {
    e.preventDefault()

    setIsLoading(true)

    try {
      await post('/extend-admin-api/normalize', {
        type: singularName,
        documentIds: [],
      })

      setIsLoading(false)
  message.success('标准化成功，请刷新页面查看。')

    }
    catch (err) {
      if (err.response?.data?.error?.details?.filePath) {
        download(err.response.data.error.details.filePath, {
          filename: `${singularName}-export`,
        })
      }
      setIsLoading(false)
  message.error(err?.response?.data?.error?.message || err.message)
    }
  }

  return (
    <Popconfirm
    title="标准化处理"
    description="将对所有数据进行标准化处理，数据量大时会速度较慢，是否确认？"
    onConfirm={onConfirm}
    okText="确认"
    cancelText="取消"
  >
        <Button
          icon={<PartitionOutlined />}
          loading={isLoading}
        >
          标准化
        </Button>
  </Popconfirm>
        )
}

export { NormaliseButton }
