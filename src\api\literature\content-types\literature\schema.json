{"kind": "collectionType", "collectionName": "literatures", "info": {"description": "", "displayName": "海外文献库/元数据", "pluralName": "literatures", "singularName": "literature"}, "options": {"draftAndPublish": true}, "extMd5Field": ["host", "detail_url", "source", "title", "pub_year"], "extIsStandardizedData": true, "extUniqueField": ["identifier"], "pluginOptions": {}, "attributes": {"content_type_es": {"type": "string", "default": "literature", "extIsValidationRequired": false, "extIsFieldExported": false}, "abstract": {"extExcelTitle": "摘要", "type": "text"}, "access_type": {"extExcelTitle": "获取方式", "required": true, "type": "string", "regex": "^(?!\\s*$).+", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "access_type_normal", "tableName": "enum-literature-access-type", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "access_type_normal": {"type": "string", "extIsValidationRequired": false, "extIsFieldExported": false}, "author": {"type": "text", "extExcelTitle": "作者"}, "author_org": {"type": "text", "extExcelTitle": "作者机构"}, "caas_discipline": {"type": "string", "extExcelTitle": "CAAS学科分类", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "caas_discipline_normal", "tableName": "enum-caas", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "caas_discipline_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "category": {"required": true, "type": "string", "extExcelTitle": "出版分类", "regex": "^(?!\\s*$).+", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "category_normal", "tableName": "enum-literature-category", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "category_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "collection_id": {"type": "text", "extExcelTitle": "文文献集合标识符"}, "collection_name": {"type": "text", "extExcelTitle": "文献集合名"}, "collection_time": {"type": "string", "extExcelTitle": "采集时间"}, "collection_url": {"type": "text", "extExcelTitle": "合集网址"}, "country": {"type": "string", "extExcelTitle": "国家分类", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "country_normal", "tableName": "enum-country", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "country_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "cover": {"type": "text", "extExcelTitle": "封面", "extRelatedTable": {"associatedFieldName": "cover_normal", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:"}}, "cover_normal": {"type": "text", "extIsValidationRequired": false}, "detail_url": {"required": true, "extExcelTitle": "采集页面网址", "type": "text", "regex": "^(?!\\s*$).+"}, "doi": {"type": "string", "extExcelTitle": "doi"}, "files": {"type": "text", "extExcelTitle": "文件名", "extRelatedTable": {"associatedFieldName": "files_normal", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:"}}, "files_normal": {"type": "text", "extIsFieldExported": false, "extIsValidationRequired": false}, "identifier": {"type": "text", "required": true, "unique": true, "extExcelTitle": "文献唯一标识符", "regex": "^(?!\\s*$).+"}, "images": {"type": "text", "extExcelTitle": "图片名称", "extRelatedTable": {"associatedFieldName": "images_normal", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:"}}, "images_normal": {"type": "text", "extIsFieldExported": false, "extIsValidationRequired": false}, "industry": {"type": "string", "extExcelTitle": "行业类别", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "industry_normal", "tableName": "enum-literature-industry", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "industry_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "isbn": {"type": "string", "extExcelTitle": "isbn"}, "issn": {"type": "string", "extExcelTitle": "issn"}, "keyword": {"type": "text", "extExcelTitle": "关键词"}, "language": {"required": true, "type": "string", "extExcelTitle": "语种", "regex": "^(?!\\s*$).+", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "language_normal", "tableName": "enum-literature-language", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "language_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "normed": {"type": "boolean", "extIsFieldExported": false, "extIsValidationRequired": false, "default": false}, "other_data": {"type": "text", "extExcelTitle": "其他信息"}, "page_info": {"type": "string", "extExcelTitle": "页码范围"}, "pub_date": {"type": "string", "extExcelTitle": "出版日期"}, "pub_year": {"type": "string", "extExcelTitle": "发布年份"}, "publish_cycle": {"type": "string", "extExcelTitle": "出版周期", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "publish_cycle_normal", "tableName": "enum-literature-publish-cycle", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "publish_cycle_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "remark": {"type": "text", "extExcelTitle": "备注"}, "risk_rating": {"required": true, "type": "string", "extExcelTitle": "风险等级", "regex": "^(?!\\s*$).+", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "risk_rating_normal", "tableName": "enum-literature-risk-rating", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "risk_rating_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}, "source": {"required": true, "type": "text", "extExcelTitle": "来源网站", "regex": "^(?!\\s*$).+", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "source_normal", "tableName": "enum-source", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "code", "name_en", "name_short", "host"]}}, "source_normal": {"type": "text", "extIsFieldExported": false, "extIsValidationRequired": false}, "subtitle": {"type": "text", "extExcelTitle": "副标题"}, "support_project": {"type": "text", "extExcelTitle": "支持项目"}, "title": {"required": true, "type": "text", "extExcelTitle": "标题"}, "topic": {"type": "string", "extExcelTitle": "文献主题"}, "unique_md5": {"type": "string", "extIsFieldExported": false}, "use_type": {"required": true, "type": "string", "extExcelTitle": "使用权限", "regex": "^(?!\\s*$).+", "extIsNormal": true, "extRelatedTable": {"associatedFieldName": "use_type_normal", "tableName": "enum-literature-use-type", "separator": "||", "splitCharacters": "|-|", "endingCharacter": ":-:", "validateField": ["name", "name_en", "code", "name1", "name2", "name3", "name3", "name4", "name5", "name6", "name7", "name8", "name9"]}}, "use_type_normal": {"type": "string", "extIsFieldExported": false, "extIsValidationRequired": false}}}